# BetterP 全局任务板（可勾选）

> 更新规则：每次推进后立刻在此勾选/更新状态；周例会统一核对。详情请参考：docs/README.md
> 单一真相源：计划以 docs/development-plan-revised.md 为准；状态以 docs/current-status.md 为准。

## 0. 项目总览
- 阶段：Phase 2（核心功能迭代中）
- 上次更新时间：YYYY-MM-DD（请更新）
- 当前负责人：@owner（请指定）

## 1. 里程碑（Milestones）
- [x] M1 核心基础搭建完成（用户系统、任务管理、项目管理 基础后端能力）
- [ ] M2 基础应用完成（第10周）- 可独立使用的任务管理应用（前端可用）
- [ ] M3 AI 增强功能完成（第15周）- 自然语言/建议/对话
- [ ] M4 发布就绪（第17周）- 测试、部署、上线准备

## 2. 已完成基础（根据文档现状汇总）
- [x] 环境/数据库/缓存/CI：Python 3.12、PostgreSQL、Redis、Docker、CI流水线
- [x] 后端框架：FastAPI 项目结构、统一错误处理、OpenAPI 文档
- [x] 认证与会话：JWT 登录/注册/会话（如有偏差请修正状态）
- [x] 任务基础：任务 CRUD（如有偏差请修正状态）
- [x] 通信能力：SSE 推送、事件管理、文件上传

## 3. 垂直切片执行板（前后端并行，小步快跑）
> DoD：后端API稳定+最小单测+OpenAPI；前端界面可用+联调通过；关键路径用例；文档同步；最简监控
> WIP≤2；分支：feature/slice-<序号>-<名称>

### 切片 1：用户认证（最小闭环）
- [ ] 后端：注册/登录/刷新Token/受保护路由中间件
- [ ] 前端：登录/注册页、Token 持久化、拦截器、登录后跳转
- [ ] 联调：注册→登录→访问受保护接口→退出 全流程
- [ ] 文档：current-status 与计划更新

### 切片 2：任务 CRUD（最小任务流）
- [ ] 后端：/tasks CRUD、分页、校验
- [ ] 前端：任务列表/新建/编辑/删除、空/加载/错态
- [ ] 联调：创建→显示→编辑→删除
- [ ] 文档：示例请求/响应更新

### 切片 3：项目分组与筛选
- [ ] 后端：/projects CRUD、任务关联、筛选参数（project_id/status/due）
- [ ] 前端：项目切换、筛选控件、条件持久化
- [ ] 联调：创建项目→任务归属→筛选正确
- [ ] 文档：筛选用例与边界

### 切片 4：提醒与通知（最简实现）
- [ ] 后端：基础提醒规则（计划任务/轮询均可）、到点触发应用内通知
- [ ] 前端：通知中心页、任务卡提醒标识
- [ ] 联调：到点提醒→标记已读/已处理
- [ ] 文档：提醒策略与限制说明

### 切片 5：基础统计（价值反馈）
- [ ] 后端：/stats 概览（完成数、进行中、按项目计数）
- [ ] 前端：Dashboard 小卡片 + 1–2 图表
- [ ] 联调：新增/完成任务→统计变化正确
- [ ] 文档：指标口径与示例

### 切片 6：文件附件（最小文件流）
- [ ] 后端：上传接口+元信息表+任务关联；受权下载地址
- [ ] 前端：任务详情附件区：上传/列表/预览
- [ ] 联调：上传→详情可见→下载/预览
- [ ] 文档：大小/类型/安全策略

### 切片 7：体验打磨与稳定性
- [ ] 后端：慢查询与 N+1 治理、必要索引、错误码统一
- [ ] 前端：错误处理与重试、列表性能优化
- [ ] 联调：关键路径冒烟/回归脚本化
- [ ] 文档：最佳实践与问题清单

## 4. 领域任务池（分面视图，支持穿插安排）
### 用户与认证
- [ ] 第三方登录（可选）
- [ ] 密码策略与安全加固

### 任务与项目
- [ ] 任务重复/提醒规则扩展（工作日/自定义）
- [ ] 批量操作与快捷动作

### 通知与协作
- [ ] 推送渠道扩展（邮件/系统推送）
- [ ] 基础协作（指派/关注/评论）

### 统计与可视化
- [ ] 趋势图与分段统计
- [ ] 数据导出（CSV/JSON）

### 附件与存储
- [ ] 云存储集成（S3/OSS）
- [ ] 版本与权限

### AI（后续增强）
- [ ] 自然语言创建任务（与切片集成）
- [ ] 智能建议与优先级提示

## 5. 质量保障与工程健康
- [ ] 后端：单元测试 ≥ 核心模块；接口契约测试；OpenAPI 自动校验
- [ ] 前端：Widget/集成测试覆盖关键路径
- [ ] 静态检查：lint/type-check 全通过方可合并
- [ ] 可观测性：关键日志/错误上报；慢查询监控
- [ ] 安全：依赖升级策略；Secrets 管理；权限最小化

## 6. 运维与发布
- [ ] Docker Compose 本地环境标准化与脚本
- [ ] 预生产环境与最小监控（可选）
- [ ] 发布清单：版本号、变更摘要、迁移脚本检查

## 7. 文档治理
- [ ] docs/current-status.md 每周更新
- [ ] docs/development-plan-revised.md 与本任务板保持一致
- [ ] docs/README.md 持续补充索引
- [ ] ADR（关键决策记录）目录建立（可选）

---
提示：请优先以“切片 1/2/3”为第一轮目标；WIP≤2，完成一条再开下一条。
