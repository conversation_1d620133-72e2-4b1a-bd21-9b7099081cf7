<svg width="1500" height="1050" xmlns="http://www.w3.org/2000/svg">
  <g transform="scale(1.5)">
  <defs>
    <linearGradient id="collectGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#27AE60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="analyzeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E74C3C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C0392B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="assessGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F39C12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E67E22;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="optimizeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9B59B6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8E44AD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="learnGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498DB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980B9;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="700" fill="#f8f9fa"/>
  
  <!-- Title -->
  <text x="500" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">熵减引擎架构图</text>
  
  <!-- Data Collection Module -->
  <g transform="translate(50, 60)">
    <rect x="0" y="0" width="900" height="100" rx="10" fill="url(#collectGradient)" stroke="#229954" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">数据采集模块</text>
    
    <!-- Collection modules -->
    <rect x="20" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="105" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">行为数据记录</text>
    <text x="25" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 任务执行时间</text>
    <text x="25" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 专注度监测</text>
    
    <rect x="210" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="295" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">生理指标监测</text>
    <text x="215" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 心率变异性</text>
    <text x="215" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 睡眠质量</text>
    
    <rect x="400" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="485" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">环境因素采集</text>
    <text x="405" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 时间环境</text>
    <text x="405" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 空间环境</text>
    
    <rect x="590" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="675" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">主观状态评估</text>
    <text x="595" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 情绪状态</text>
    <text x="595" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 能量水平</text>
    
    <rect x="780" y="35" width="100" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="830" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">实时监控</text>
    <text x="785" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 连续采集</text>
    <text x="785" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 异常检测</text>
  </g>
  
  <!-- Pattern Analysis Engine -->
  <g transform="translate(50, 180)">
    <rect x="0" y="0" width="900" height="100" rx="10" fill="url(#analyzeGradient)" stroke="#C0392B" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">模式分析引擎</text>
    
    <!-- Analysis modules -->
    <rect x="20" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="105" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">时间序列分析</text>
    <text x="25" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 趋势分析</text>
    <text x="25" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 周期性识别</text>
    
    <rect x="210" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="295" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">聚类分析</text>
    <text x="215" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• K-means聚类</text>
    <text x="215" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• DBSCAN密度聚类</text>
    
    <rect x="400" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="485" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">关联规则挖掘</text>
    <text x="405" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• Apriori算法</text>
    <text x="405" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 序列模式挖掘</text>
    
    <rect x="590" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="675" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">异常检测</text>
    <text x="595" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 统计方法</text>
    <text x="595" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 机器学习方法</text>
    
    <rect x="780" y="35" width="100" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="830" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">模式识别</text>
    <text x="785" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 习惯模式</text>
    <text x="785" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 效率模式</text>
  </g>
  
  <!-- Efficiency Assessment System -->
  <g transform="translate(50, 300)">
    <rect x="0" y="0" width="900" height="100" rx="10" fill="url(#assessGradient)" stroke="#E67E22" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">效率评估系统</text>
    
    <!-- Assessment modules -->
    <rect x="20" y="35" width="280" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="160" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">多维效率指标</text>
    <text x="25" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 任务完成质量 • 时间利用效率</text>
    <text x="25" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 能量消耗比 • 满意度得分</text>
    
    <rect x="320" y="35" width="280" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="460" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">基准对比分析</text>
    <text x="325" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 历史表现对比 • 同类用户对比</text>
    <text x="325" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 理想状态对比 • 行业标准对比</text>
    
    <rect x="620" y="35" width="260" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="750" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">趋势预测模型</text>
    <text x="625" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• LSTM预测网络</text>
    <text x="625" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 时间序列预测</text>
  </g>
  
  <!-- Optimization Strategy Generator -->
  <g transform="translate(50, 420)">
    <rect x="0" y="0" width="900" height="100" rx="10" fill="url(#optimizeGradient)" stroke="#8E44AD" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">优化策略生成器</text>
    
    <!-- Strategy modules -->
    <rect x="20" y="35" width="210" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="125" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">个性化建议算法</text>
    <text x="25" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 协同过滤推荐</text>
    <text x="25" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 基于内容推荐</text>
    
    <rect x="250" y="35" width="210" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="355" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">A/B测试框架</text>
    <text x="255" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 实验设计</text>
    <text x="255" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 结果分析</text>
    
    <rect x="480" y="35" width="210" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="585" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">渐进式改进策略</text>
    <text x="485" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 微调优化</text>
    <text x="485" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 习惯养成</text>
    
    <rect x="710" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="795" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">反馈循环机制</text>
    <text x="715" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 实时反馈</text>
    <text x="715" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 周期性总结</text>
  </g>
  
  <!-- Adaptive Learning System -->
  <g transform="translate(50, 540)">
    <rect x="0" y="0" width="900" height="100" rx="10" fill="url(#learnGradient)" stroke="#2980B9" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">自适应学习系统</text>
    
    <!-- Learning modules -->
    <rect x="20" y="35" width="210" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="125" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">强化学习模型</text>
    <text x="25" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• Q-Learning算法</text>
    <text x="25" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 深度强化学习</text>
    
    <rect x="250" y="35" width="210" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="355" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">用户反馈整合</text>
    <text x="255" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 显式反馈处理</text>
    <text x="255" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 隐式反馈挖掘</text>
    
    <rect x="480" y="35" width="210" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="585" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">策略效果评估</text>
    <text x="485" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 效果度量指标</text>
    <text x="485" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 因果推断分析</text>
    
    <rect x="710" y="35" width="170" height="55" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="795" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">模型持续优化</text>
    <text x="715" y="70" font-family="Arial, sans-serif" font-size="8" fill="white">• 在线学习</text>
    <text x="715" y="82" font-family="Arial, sans-serif" font-size="8" fill="white">• 自动调参</text>
  </g>
  
  <!-- Data Flow Arrows -->
  <!-- Collection to Analysis -->
  <path d="M 500 160 L 500 180" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Analysis to Assessment -->
  <path d="M 500 280 L 500 300" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Assessment to Strategy -->
  <path d="M 500 400 L 500 420" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Strategy to Learning -->
  <path d="M 500 520 L 500 540" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Learning feedback loop -->
  <path d="M 50 620 Q 30 620 30 110 Q 30 60 50 110" stroke="#E67E22" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- Strategy feedback to Assessment -->
  <path d="M 950 470 Q 970 470 970 350 Q 970 300 950 350" stroke="#16A085" stroke-width="2" fill="none" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
  
  <!-- Right side optimization types -->
  <g transform="translate(850, 160)">
    <rect x="0" y="0" width="130" height="360" rx="5" fill="rgba(52, 152, 219, 0.1)" stroke="#3498DB" stroke-width="1"/>
    <text x="65" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">优化策略类型</text>
    
    <rect x="10" y="30" width="110" height="25" rx="3" fill="#E8F8F5" stroke="#27AE60" stroke-width="1"/>
    <text x="65" y="47" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#27AE60">能量管理优化</text>
    
    <rect x="10" y="65" width="110" height="25" rx="3" fill="#FEF9E7" stroke="#F39C12" stroke-width="1"/>
    <text x="65" y="82" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#F39C12">注意力流优化</text>
    
    <rect x="10" y="100" width="110" height="25" rx="3" fill="#FDEDEC" stroke="#E74C3C" stroke-width="1"/>
    <text x="65" y="117" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#E74C3C">习惯养成系统</text>
    
    <rect x="10" y="135" width="110" height="25" rx="3" fill="#F4ECF7" stroke="#9B59B6" stroke-width="1"/>
    <text x="65" y="152" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#9B59B6">时间模式识别</text>
    
    <rect x="10" y="170" width="110" height="25" rx="3" fill="#EBF5FB" stroke="#3498DB" stroke-width="1"/>
    <text x="65" y="187" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#3498DB">决策疲劳减少</text>
    
    <rect x="10" y="205" width="110" height="25" rx="3" fill="#E8F6F3" stroke="#1ABC9C" stroke-width="1"/>
    <text x="65" y="222" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1ABC9C">环境优化建议</text>
    
    <text x="65" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">核心算法</text>
    <text x="15" y="265" font-family="Arial, sans-serif" font-size="8" fill="#34495e">• 行为模式挖掘</text>
    <text x="15" y="280" font-family="Arial, sans-serif" font-size="8" fill="#34495e">• 效率瓶颈识别</text>
    <text x="15" y="295" font-family="Arial, sans-serif" font-size="8" fill="#34495e">• 个性化推荐</text>
    <text x="15" y="310" font-family="Arial, sans-serif" font-size="8" fill="#34495e">• 强化学习优化</text>
    <text x="15" y="325" font-family="Arial, sans-serif" font-size="8" fill="#34495e">• 因果关系推断</text>
    <text x="15" y="340" font-family="Arial, sans-serif" font-size="8" fill="#34495e">• 预测模型训练</text>
  </g>
  
  <!-- Bottom legend -->
  <g transform="translate(50, 660)">
    <rect x="0" y="0" width="780" height="30" rx="3" fill="rgba(255,255,255,0.9)" stroke="#ddd" stroke-width="1"/>
    <text x="10" y="15" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">熵减引擎核心理念:</text>
    <text x="10" y="25" font-family="Arial, sans-serif" font-size="9" fill="#34495e">通过持续监控、模式分析、效率评估、策略优化和自适应学习，实现系统性效率提升和个人行为优化</text>
    
    <text x="500" y="15" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">数据流向:</text>
    <line x1="570" y1="12" x2="585" y2="12" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <text x="590" y="15" font-family="Arial, sans-serif" font-size="9" fill="#34495e">正向流</text>
    <line x1="640" y1="12" x2="655" y2="12" stroke="#E67E22" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="660" y="15" font-family="Arial, sans-serif" font-size="9" fill="#34495e">反馈流</text>
  </g>
  </g>
</svg>