<svg width="1500" height="1050" xmlns="http://www.w3.org/2000/svg">
  <g transform="scale(1.5)">
  <defs>
    <linearGradient id="parseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F7931E;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="coreGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1ABC9C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16A085;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="priorityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8E44AD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9B59B6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="planGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498DB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980B9;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="700" fill="#f8f9fa"/>
  
  <!-- Title -->
  <text x="500" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">动态任务拆解引擎架构图</text>
  
  <!-- Task Parser -->
  <g transform="translate(50, 60)">
    <rect x="0" y="0" width="900" height="110" rx="10" fill="url(#parseGradient)" stroke="#F7931E" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">任务解析器</text>
    
    <!-- Parser modules -->
    <rect x="20" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="120" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">任务描述理解</text>
    <text x="30" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 关键词提取</text>
    <text x="30" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 动作识别</text>
    <text x="30" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 目标解析</text>
    
    <rect x="240" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="340" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">复杂度评估</text>
    <text x="250" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 认知复杂度模型</text>
    <text x="250" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 时间复杂度预测</text>
    <text x="250" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 资源复杂度分析</text>
    
    <rect x="460" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="560" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">时间预估模型</text>
    <text x="470" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 历史数据回归模型</text>
    <text x="470" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 专家系统规则</text>
    <text x="470" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 不确定性量化</text>
    
    <rect x="680" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="780" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">资源需求分析</text>
    <text x="690" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 人力资源需求</text>
    <text x="690" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 工具设备需求</text>
    <text x="690" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 信息资源需求</text>
  </g>
  
  <!-- Core Algorithm -->
  <g transform="translate(50, 190)">
    <rect x="0" y="0" width="900" height="110" rx="10" fill="url(#coreGradient)" stroke="#16A085" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">拆解算法核心</text>
    
    <!-- Core modules -->
    <rect x="20" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="120" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">层次化分解树</text>
    <text x="30" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 目标分解算法</text>
    <text x="30" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 子任务生成规则</text>
    <text x="30" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 原子化检查机制</text>
    
    <rect x="240" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="340" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">依赖关系解析</text>
    <text x="250" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 依赖关系图构建</text>
    <text x="250" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 循环依赖检测</text>
    <text x="250" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 关键路径计算</text>
    
    <rect x="460" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="560" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">里程碑提取</text>
    <text x="470" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 关键节点识别</text>
    <text x="470" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 验收标准定义</text>
    <text x="470" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 风险检查点设置</text>
    
    <rect x="680" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="780" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">可行性验证</text>
    <text x="690" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 资源可用性检查</text>
    <text x="690" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 时间窗口验证</text>
    <text x="690" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 技能匹配评估</text>
  </g>
  
  <!-- Priority Engine -->
  <g transform="translate(50, 320)">
    <rect x="0" y="0" width="900" height="110" rx="10" fill="url(#priorityGradient)" stroke="#9B59B6" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">优先级引擎</text>
    
    <!-- Priority modules -->
    <rect x="20" y="35" width="280" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="160" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">多维度权重计算</text>
    <text x="30" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 时间紧迫度 • 重要性评分</text>
    <text x="30" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 用户能量状态 • 资源可用性</text>
    <text x="30" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 影响范围 • 价值贡献</text>
    
    <rect x="320" y="35" width="280" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="460" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">动态调整算法</text>
    <text x="330" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 实时优先级重计算</text>
    <text x="330" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 突发事件响应机制</text>
    <text x="330" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 用户反馈整合</text>
    
    <rect x="620" y="35" width="260" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="750" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">冲突解决机制</text>
    <text x="630" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 资源冲突检测</text>
    <text x="630" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 时间冲突解决</text>
    <text x="630" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 优先级冲突仲裁</text>
  </g>
  
  <!-- Plan Generator -->
  <g transform="translate(50, 450)">
    <rect x="0" y="0" width="900" height="110" rx="10" fill="url(#planGradient)" stroke="#2980B9" stroke-width="2" filter="url(#shadow)"/>
    <text x="450" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">执行计划生成器</text>
    
    <!-- Plan modules -->
    <rect x="20" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="120" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">时间窗口分配</text>
    <text x="30" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 可用时间分析</text>
    <text x="30" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 最优时间匹配</text>
    <text x="30" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 缓冲时间设置</text>
    
    <rect x="240" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="340" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">提醒策略制定</text>
    <text x="250" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 提醒时机计算</text>
    <text x="250" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 提醒方式选择</text>
    <text x="250" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 提醒频率设定</text>
    
    <rect x="460" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="560" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">进度跟踪设计</text>
    <text x="470" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 进度指标定义</text>
    <text x="470" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 监控点设置</text>
    <text x="470" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 自动进度更新</text>
    
    <rect x="680" y="35" width="200" height="65" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="780" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">自适应调整</text>
    <text x="690" y="70" font-family="Arial, sans-serif" font-size="9" fill="white">• 执行反馈收集</text>
    <text x="690" y="82" font-family="Arial, sans-serif" font-size="9" fill="white">• 计划偏差分析</text>
    <text x="690" y="94" font-family="Arial, sans-serif" font-size="9" fill="white">• 动态重规划</text>
  </g>
  
  <!-- Data Flow Arrows -->
  <!-- Parser to Core -->
  <path d="M 500 170 L 500 190" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Core to Priority -->
  <path d="M 500 300 L 500 320" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Priority to Plan -->
  <path d="M 500 430 L 500 450" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Feedback loops -->
  <path d="M 50 540 Q 30 540 30 350 Q 30 150 50 130" stroke="#E67E22" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- Task Type Classification (Left side) -->
  <g transform="translate(20, 580)">
    <rect x="0" y="0" width="460" height="100" rx="5" fill="rgba(52, 152, 219, 0.1)" stroke="#3498DB" stroke-width="1"/>
    <text x="230" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2c3e50">任务类型智能识别</text>
    
    <rect x="10" y="30" width="65" height="30" rx="3" fill="#E8F8F5" stroke="#1ABC9C" stroke-width="1"/>
    <text x="42" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1ABC9C">项目任务</text>
    
    <rect x="85" y="30" width="65" height="30" rx="3" fill="#FEF9E7" stroke="#F1C40F" stroke-width="1"/>
    <text x="117" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#F39C12">提醒任务</text>
    
    <rect x="160" y="30" width="65" height="30" rx="3" fill="#EBF5FB" stroke="#3498DB" stroke-width="1"/>
    <text x="192" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#3498DB">会议任务</text>
    
    <rect x="235" y="30" width="65" height="30" rx="3" fill="#FDEDEC" stroke="#E74C3C" stroke-width="1"/>
    <text x="267" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#E74C3C">工作任务</text>
    
    <rect x="310" y="30" width="65" height="30" rx="3" fill="#F4ECF7" stroke="#9B59B6" stroke-width="1"/>
    <text x="342" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#9B59B6">生活任务</text>
    
    <rect x="385" y="30" width="65" height="30" rx="3" fill="#E8F6F3" stroke="#16A085" stroke-width="1"/>
    <text x="417" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#16A085">运动任务</text>
    
    <text x="10" y="75" font-family="Arial, sans-serif" font-size="9" fill="#34495e">• 长期多阶段项目的层次化拆解</text>
    <text x="10" y="88" font-family="Arial, sans-serif" font-size="9" fill="#34495e">• 基于任务特征的智能分类和处理策略</text>
  </g>
  
  <!-- Optimization Strategy (Right side) -->
  <g transform="translate(520, 580)">
    <rect x="0" y="0" width="460" height="100" rx="5" fill="rgba(231, 76, 60, 0.1)" stroke="#E74C3C" stroke-width="1"/>
    <text x="230" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2c3e50">动态优化策略</text>
    
    <text x="10" y="40" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">实时调整机制:</text>
    <text x="10" y="55" font-family="Arial, sans-serif" font-size="9" fill="#34495e">• 外部环境变化响应 • 用户状态动态感知 • 突发事件处理</text>
    
    <text x="250" y="40" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">效率优化算法:</text>
    <text x="250" y="55" font-family="Arial, sans-serif" font-size="9" fill="#34495e">• 并行任务识别 • 资源利用最大化 • 关键路径优化</text>
    
    <text x="10" y="75" font-family="Arial, sans-serif" font-size="9" fill="#34495e">• 基于用户历史数据的个性化时间预估</text>
    <text x="10" y="88" font-family="Arial, sans-serif" font-size="9" fill="#34495e">• 多维度权重计算实现智能优先级排序</text>
  </g>
  </g>
</svg>