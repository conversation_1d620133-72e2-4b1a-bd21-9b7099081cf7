# 文档索引（Docs Index）

本项目采用“单一真相源（SSoT）+ 垂直切片并行”的文档治理与开发方式。

- 权威计划：docs/development-plan-revised.md（唯一生效的开发计划）
- 权威状态板：docs/current-status.md（每次推进后更新）

## 快速入口
- **开发任务追踪板**：docs/development-tasks.md（🎯 唯一任务管理文档，前后端并行）
- **智能助手完整计划**：docs/smart-assistant-plan.md（📋 产品定位与技术方案）
- 当前开发状态板：docs/current-status.md（📊 周状态总结）

## 开发原则
- **唯一文档**: 只使用 development-tasks.md 管理任务，避免同步问题
- **前后端并行**: 每个功能都要前后端同步开发和测试
- **小步快跑**: 每个任务1-2天完成，立即验证
- **逐一测试**: 每个功能点都要端到端测试通过
- 应用架构说明：docs/application-architecture.md
- 全面开发指南（总览）：docs/development-guide.md
- 调试记录：docs/debugging-notes.md
- 大规模与性能专题：docs/architecture-100k-users.md, docs/large-scale-optimization.md
- 支付与商业化专题：docs/payment-system-architecture.md
- 关键架构图：
  - 系统总览：docs/system-architecture.svg
  - 对话控制引擎：docs/dialogue-control-engine.svg
  - 任务拆解引擎：docs/task-decomposition-engine.svg
  - 熵减引擎：docs/entropy-reduction-engine.svg

## 文档约定
- 计划类：以修订版为准，旧版 development-plan.md 已标记“已归档”。
- 状态类：以 current-status.md 为唯一权威；如有摘要文件，仅做镜像不单独维护。
- 变更记录：重要策略/阶段调整需更新修订版计划顶部“执行方法”和“下一步行动”。

## 更新职责
- 每周例会后更新：current-status.md（阶段、焦点任务、阻塞、环境）
- 每个切片完成后：在修订版计划中勾选对应切片，并记录验收要点
- 架构重大调整：更新 application-architecture.md 并在 docs/README.md 补链接

## 建议的开发方式（摘要）
- 采用垂直切片 + 前后端并行，小步快跑；WIP≤2；DoD 覆盖后端/前端/联调/文档/监控
- 端到端路径优先：认证 → 任务 CRUD → 项目/筛选 → 提醒 → 统计 → 附件 → 打磨

