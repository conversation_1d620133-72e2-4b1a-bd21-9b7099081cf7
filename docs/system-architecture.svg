<svg width="1800" height="1200" xmlns="http://www.w3.org/2000/svg">
  <g transform="scale(1.5)">
  <defs>
    <linearGradient id="engineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7ED321;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5BA517;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="platformGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F5A623;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C7851B;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- Title -->
  <text x="600" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">智能生活管理应用系统架构</text>
  
  <!-- User Interface Layer -->
  <rect x="50" y="80" width="1100" height="60" rx="10" fill="#E8F4FD" stroke="#4A90E2" stroke-width="2" filter="url(#shadow)"/>
  <text x="600" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">用户交互界面层</text>
  <text x="150" y="125" font-family="Arial, sans-serif" font-size="12" fill="#34495e">Web界面</text>
  <text x="300" y="125" font-family="Arial, sans-serif" font-size="12" fill="#34495e">移动应用</text>
  <text x="450" y="125" font-family="Arial, sans-serif" font-size="12" fill="#34495e">语音助手</text>
  <text x="600" y="125" font-family="Arial, sans-serif" font-size="12" fill="#34495e">智能设备</text>
  <text x="750" y="125" font-family="Arial, sans-serif" font-size="12" fill="#34495e">API接口</text>
  <text x="900" y="125" font-family="Arial, sans-serif" font-size="12" fill="#34495e">第三方集成</text>
  
  <!-- Core Engines Layer -->
  <g transform="translate(0, 160)">
    <!-- Dialog Control Engine -->
    <rect x="80" y="20" width="300" height="120" rx="10" fill="url(#engineGradient)" stroke="#357ABD" stroke-width="2" filter="url(#shadow)"/>
    <text x="230" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">对话控制中枢</text>
    <text x="90" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">• 多模态输入处理</text>
    <text x="90" y="80" font-family="Arial, sans-serif" font-size="11" fill="white">• 意图理解与识别</text>
    <text x="90" y="95" font-family="Arial, sans-serif" font-size="11" fill="white">• 上下文记忆管理</text>
    <text x="90" y="110" font-family="Arial, sans-serif" font-size="11" fill="white">• 个性化交互适应</text>
    <text x="90" y="125" font-family="Arial, sans-serif" font-size="11" fill="white">• 自然语言生成</text>
    
    <!-- Task Decomposition Engine -->
    <rect x="420" y="20" width="300" height="120" rx="10" fill="url(#engineGradient)" stroke="#357ABD" stroke-width="2" filter="url(#shadow)"/>
    <text x="570" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">动态任务拆解引擎</text>
    <text x="430" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">• 智能任务识别分类</text>
    <text x="430" y="80" font-family="Arial, sans-serif" font-size="11" fill="white">• 层次化任务分解</text>
    <text x="430" y="95" font-family="Arial, sans-serif" font-size="11" fill="white">• 依赖关系分析</text>
    <text x="430" y="110" font-family="Arial, sans-serif" font-size="11" fill="white">• 优先级动态调整</text>
    <text x="430" y="125" font-family="Arial, sans-serif" font-size="11" fill="white">• 执行计划生成</text>
    
    <!-- Entropy Reduction Engine -->
    <rect x="760" y="20" width="300" height="120" rx="10" fill="url(#engineGradient)" stroke="#357ABD" stroke-width="2" filter="url(#shadow)"/>
    <text x="910" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">熵减引擎</text>
    <text x="770" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">• 行为模式分析</text>
    <text x="770" y="80" font-family="Arial, sans-serif" font-size="11" fill="white">• 效率指标监控</text>
    <text x="770" y="95" font-family="Arial, sans-serif" font-size="11" fill="white">• 瓶颈识别诊断</text>
    <text x="770" y="110" font-family="Arial, sans-serif" font-size="11" fill="white">• 优化策略生成</text>
    <text x="770" y="125" font-family="Arial, sans-serif" font-size="11" fill="white">• 持续学习优化</text>
  </g>
  
  <!-- AI Platform Layer -->
  <g transform="translate(0, 320)">
    <rect x="80" y="20" width="980" height="80" rx="10" fill="url(#platformGradient)" stroke="#C7851B" stroke-width="2" filter="url(#shadow)"/>
    <text x="570" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">AI平台服务层</text>
    <text x="120" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">机器学习平台</text>
    <text x="250" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">深度学习服务</text>
    <text x="380" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">自然语言处理</text>
    <text x="520" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">推荐系统</text>
    <text x="650" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">知识图谱</text>
    <text x="780" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">模型管理</text>
    <text x="900" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">特征工程</text>
    
    <text x="120" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">实时计算</text>
    <text x="250" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">批处理分析</text>
    <text x="380" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">多模态融合</text>
    <text x="520" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">个性化引擎</text>
    <text x="650" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">语义理解</text>
    <text x="780" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">A/B测试</text>
    <text x="900" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">自动调参</text>
  </g>
  
  <!-- Data Storage Layer -->
  <g transform="translate(0, 440)">
    <rect x="80" y="20" width="980" height="100" rx="10" fill="url(#dataGradient)" stroke="#5BA517" stroke-width="2" filter="url(#shadow)"/>
    <text x="570" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">数据存储层</text>
    
    <!-- Database Types -->
    <rect x="120" y="55" width="150" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="195" y="72" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">关系数据库</text>
    <text x="195" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">PostgreSQL</text>
    
    <rect x="290" y="55" width="150" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="365" y="72" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">时序数据库</text>
    <text x="365" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">InfluxDB</text>
    
    <rect x="460" y="55" width="150" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="535" y="72" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">缓存数据库</text>
    <text x="535" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Redis</text>
    
    <rect x="630" y="55" width="150" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="705" y="72" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">文档数据库</text>
    <text x="705" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">MongoDB</text>
    
    <rect x="800" y="55" width="150" height="40" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="875" y="72" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">图数据库</text>
    <text x="875" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Neo4j</text>
  </g>
  
  <!-- Infrastructure Layer -->
  <g transform="translate(0, 580)">
    <rect x="80" y="20" width="980" height="80" rx="10" fill="#95A5A6" stroke="#7F8C8D" stroke-width="2" filter="url(#shadow)"/>
    <text x="570" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">基础设施层</text>
    
    <text x="150" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">容器编排 (Kubernetes)</text>
    <text x="350" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">消息队列 (Kafka/RabbitMQ)</text>
    <text x="600" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">服务网格 (Istio)</text>
    <text x="850" y="65" font-family="Arial, sans-serif" font-size="11" fill="white">监控运维</text>
    
    <text x="150" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">负载均衡</text>
    <text x="350" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">API网关</text>
    <text x="600" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">配置中心</text>
    <text x="850" y="85" font-family="Arial, sans-serif" font-size="11" fill="white">日志分析</text>
  </g>
  
  <!-- Data Flow Arrows -->
  <!-- User to Engines -->
  <path d="M 230 140 L 230 180" stroke="#4A90E2" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 570 140 L 570 180" stroke="#4A90E2" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 910 140 L 910 180" stroke="#4A90E2" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Engines to AI Platform -->
  <path d="M 230 300 L 230 340" stroke="#F5A623" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 570 300 L 570 340" stroke="#F5A623" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 910 300 L 910 340" stroke="#F5A623" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- AI Platform to Data -->
  <path d="M 230 420 L 230 460" stroke="#7ED321" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 570 420 L 570 460" stroke="#7ED321" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 910 420 L 910 460" stroke="#7ED321" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Data to Infrastructure -->
  <path d="M 570 560 L 570 600" stroke="#95A5A6" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Horizontal connections between engines -->
  <path d="M 380 240 L 420 240" stroke="#4A90E2" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 720 240 L 760 240" stroke="#4A90E2" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 760 220 L 420 220" stroke="#4A90E2" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4A90E2" />
    </marker>
  </defs>
  
  <!-- Legend -->
  <g transform="translate(50, 720)">
    <rect x="0" y="0" width="300" height="60" rx="5" fill="rgba(255,255,255,0.9)" stroke="#ddd" stroke-width="1"/>
    <text x="10" y="20" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">数据流向说明:</text>
    <line x1="10" y1="30" x2="25" y2="30" stroke="#4A90E2" stroke-width="2" marker-end="url(#arrowhead)"/>
    <text x="30" y="35" font-family="Arial, sans-serif" font-size="10" fill="#34495e">实时数据流</text>
    <line x1="120" y1="30" x2="135" y2="30" stroke="#4A90E2" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="140" y="35" font-family="Arial, sans-serif" font-size="10" fill="#34495e">反馈优化流</text>
    <text x="10" y="50" font-family="Arial, sans-serif" font-size="10" fill="#34495e">三大引擎协同工作，实现智能化生活管理</text>
  </g>
  </g>
</svg>