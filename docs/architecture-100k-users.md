# BetterP - 10万用户规模架构方案
## 智能生活管理应用的实用架构设计

### 🎯 规模定义和目标

#### 用户规模分析
- **目标用户**: 10万注册用户
- **日活用户**: 2-3万 (30%活跃率)
- **峰值并发**: 5000-8000用户
- **峰值QPS**: 50,000-80,000
- **平均QPS**: 10,000-20,000

#### 性能目标
- **可用性**: 99.9% (年停机时间 < 8.76小时)
- **API响应时间**: 平均 < 200ms，P99 < 1s
- **对话响应**: < 2s (包含AI推理)
- **WebSocket延迟**: < 100ms

---

## 🏗 简化的微服务架构

### 整体架构图

```
                    ┌─────────────────────────────────────┐
                    │           CDN + 负载均衡              │
                    └─────────────────┬───────────────────┘
                                     │
                    ┌─────────────────┴───────────────────┐
                    │        API Gateway                  │
                    │      (Nginx + Kong)                 │
                    └─────────────────┬───────────────────┘
                                     │
        ┌────────────────────────────┼────────────────────────────┐
        │                            │                            │
┌───────┴────────┐          ┌────────┴────────┐          ┌────────┴────────┐
│   用户服务        │          │  对话控制中枢     │          │   任务服务        │
│   (User Service) │          │ (Chat Control)  │          │ (Task Service)  │
└───────┬────────┘          └────────┬────────┘          └────────┬────────┘
        │                            │                            │
        │              ┌─────────────┴───────────────┐            │
        │              │        AI引擎服务           │            │
        │              │     (AI Engine)            │            │
        │              └─────────────┬───────────────┘            │
        │                            │                            │
        └────────────────────────────┼────────────────────────────┘
                                     │
                    ┌─────────────────┴───────────────────┐
                    │         数据存储层                   │
                    │   PostgreSQL + Redis + MongoDB      │
                    └─────────────────────────────────────┘
```

### 核心服务设计

#### 1. 对话控制中枢 (Chat Control Service)
```yaml
职责定位:
  - 用户交互的统一入口
  - 多模态输入处理 (文字、语音、图片)
  - 智能对话管理和上下文维护
  - 意图识别和指令路由
  - 响应生成和输出格式化

技术架构:
  语言: Python FastAPI + WebSocket
  AI集成: OpenAI GPT-3.5/4 + Whisper
  缓存: Redis (对话上下文)
  存储: MongoDB (对话历史)

部署配置:
  实例数: 5个Pod
  规格: 4vCPU + 8GB内存
  自动扩容: CPU > 70% 或 QPS > 2000

核心功能模块:
  - 多模态输入解析器
  - 意图识别引擎  
  - 对话状态管理器
  - 智能响应生成器
  - WebSocket连接管理器
```

#### 2. 用户服务 (User Service)
```yaml
职责:
  - 用户认证和授权
  - 用户信息管理
  - 偏好设置管理
  - 会话控制

技术栈:
  语言: Python FastAPI
  数据库: PostgreSQL (主) + Redis (缓存)
  认证: JWT Token

部署配置:
  实例数: 3个Pod
  规格: 2vCPU + 4GB内存
  数据库: 读写分离 (1主2从)
```

#### 3. 任务服务 (Task Service)  
```yaml
职责:
  - 任务CRUD操作
  - 项目管理
  - 任务拆解接口
  - 进度跟踪

技术栈:
  语言: Python FastAPI
  数据库: PostgreSQL + Redis缓存
  异步队列: Celery + Redis

部署配置:
  实例数: 4个Pod
  规格: 2vCPU + 4GB内存
  Workers: 20个Celery进程
```

#### 4. AI引擎服务 (AI Engine Service)
```yaml
职责:
  - 任务智能拆解
  - 效率分析和优化建议
  - 行为模式识别
  - 个性化推荐

技术栈:
  语言: Python + scikit-learn
  AI服务: OpenAI API + 自训练模型
  计算: CPU密集型 + GPU可选
  存储: MongoDB (模型结果)

部署配置:
  实例数: 3个Pod
  规格: 4vCPU + 16GB内存
  GPU: 可选1个T4 (成本考虑)
```

---

## 🗄 数据库架构设计

### PostgreSQL 主数据库

#### 简化的数据库架构
```yaml
部署模式: 主从复制 (1主2从)
硬件配置:
  主库: 8vCPU + 32GB内存 + 1TB SSD
  从库: 4vCPU + 16GB内存 + 500GB SSD

连接池配置:
  主库连接数: 200
  从库连接数: 400 (2×200)
  应用连接池: PgBouncer

数据量预估:
  用户数据: 10万用户 × 10KB = 1GB
  任务数据: 1000万条 × 2KB = 20GB
  项目数据: 100万个 × 5KB = 5GB
  历史数据: 按月增长约5GB
  总计: 约100GB (3年运营)
```

#### 优化策略
```sql
-- 索引优化
CREATE INDEX CONCURRENTLY idx_tasks_user_id_status ON tasks(user_id, status);
CREATE INDEX CONCURRENTLY idx_tasks_due_date ON tasks(due_date) WHERE due_date IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_chat_messages_session_id ON chat_messages(session_id, created_at DESC);

-- 分区表 (可选，数据量大时启用)
CREATE TABLE chat_messages_y2024m01 PARTITION OF chat_messages 
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 读写分离配置
-- 主库: 写操作 + 实时读取
-- 从库: 报表查询 + 历史数据分析
```

### Redis 缓存架构
```yaml
部署模式: Redis Sentinel (1主2从)
内存配置: 主16GB + 从8GB × 2
持久化: RDB + AOF

缓存策略:
  用户会话: 24小时TTL
  API响应: 30分钟TTL
  对话上下文: 2小时TTL
  任务列表: 15分钟TTL
  AI推理结果: 1小时TTL

缓存使用场景:
  - 用户认证信息
  - 频繁查询的任务列表
  - 对话上下文状态
  - API响应缓存
  - 计算结果缓存
```

### MongoDB 文档存储
```yaml
部署模式: 3节点副本集
存储配置: 每节点500GB
用途:
  - 对话历史记录
  - AI模型推理结果
  - 用户行为日志
  - 系统操作日志

数据保留策略:
  对话历史: 2年
  AI结果: 6个月
  行为日志: 1年
  系统日志: 3个月
```

---

## 🤖 对话控制中枢详细设计

### 系统架构图
```
用户输入 (文字/语音/图片)
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    对话控制中枢                               │
│                                                           │
│  ┌─────────────────┐    ┌─────────────────┐              │
│  │   输入处理器     │    │   输出生成器     │              │
│  │                │    │                │              │
│  │ • 语音转文字     │    │ • 文字回复      │              │
│  │ • 图片识别      │    │ • 语音合成      │              │
│  │ • 文本预处理    │    │ • 操作指令      │              │
│  └─────────────────┘    └─────────────────┘              │
│           │                       ↑                      │
│           ↓                       │                      │
│  ┌─────────────────┐    ┌─────────────────┐              │
│  │   意图识别器     │    │  对话状态管理    │              │
│  │                │    │                │              │
│  │ • GPT意图分析   │    │ • 上下文维护    │              │
│  │ • 实体抽取      │    │ • 会话历史      │              │
│  │ • 场景识别      │    │ • 状态机管理    │              │
│  └─────────────────┘    └─────────────────┘              │
│           │                       ↑                      │
│           ↓                       │                      │
│  ┌─────────────────────────────────────────┐              │
│  │            指令路由器                    │              │
│  │                                       │              │
│  │ • 任务管理指令 → 任务服务                │              │
│  │ • 数据查询指令 → 相应服务                │              │
│  │ • AI分析指令  → AI引擎                  │              │
│  │ • 通用对话    → GPT处理                 │              │
│  └─────────────────────────────────────────┘              │
└─────────────────────────────────────────────────────────────┘
```

### 核心功能实现

#### 1. 多模态输入处理
```python
# 输入处理器实现
from typing import Dict, Any, Optional, Union
import asyncio
import speech_recognition as sr
from PIL import Image
import openai
from dataclasses import dataclass

@dataclass
class UserInput:
    input_type: str  # 'text', 'voice', 'image'
    content: Union[str, bytes]
    user_id: str
    session_id: str
    metadata: Dict[str, Any] = None

class InputProcessor:
    def __init__(self):
        self.speech_recognizer = sr.Recognizer()
        openai.api_key = settings.OPENAI_API_KEY
    
    async def process_input(self, user_input: UserInput) -> Dict[str, Any]:
        """处理用户输入"""
        
        if user_input.input_type == 'text':
            return await self._process_text(user_input.content)
        
        elif user_input.input_type == 'voice':
            # 语音转文字
            text = await self._speech_to_text(user_input.content)
            return await self._process_text(text)
        
        elif user_input.input_type == 'image':
            # 图片识别
            return await self._process_image(user_input.content)
        
        else:
            raise ValueError(f"Unsupported input type: {user_input.input_type}")
    
    async def _speech_to_text(self, audio_data: bytes) -> str:
        """语音转文字 (使用Whisper)"""
        try:
            # 保存临时音频文件
            temp_file = f"/tmp/audio_{uuid.uuid4()}.wav"
            with open(temp_file, 'wb') as f:
                f.write(audio_data)
            
            # 调用Whisper API
            with open(temp_file, 'rb') as audio_file:
                transcript = await openai.Audio.atranscribe(
                    model="whisper-1",
                    file=audio_file,
                    language="zh"
                )
            
            return transcript.text
            
        except Exception as e:
            logger.error(f"Speech to text failed: {e}")
            return "抱歉，语音识别失败，请重试。"
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    async def _process_image(self, image_data: bytes) -> Dict[str, Any]:
        """图片处理和识别"""
        try:
            # GPT-4 Vision API
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "请描述这张图片的内容，如果是任务或待办事项相关的内容，请提取出具体的任务信息。"},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                        ]
                    }
                ]
            )
            
            description = response.choices[0].message.content
            return {
                'type': 'image_analysis',
                'description': description,
                'extracted_text': description  # 可以进一步处理提取具体信息
            }
            
        except Exception as e:
            logger.error(f"Image processing failed: {e}")
            return {
                'type': 'error',
                'message': '图片处理失败，请重试'
            }
```

#### 2. 智能意图识别
```python
# 意图识别器
from enum import Enum
import json
import re
from datetime import datetime, timedelta

class IntentType(Enum):
    CREATE_TASK = "create_task"
    UPDATE_TASK = "update_task"
    QUERY_TASKS = "query_tasks"
    DELETE_TASK = "delete_task"
    SCHEDULE_MEETING = "schedule_meeting"
    SET_REMINDER = "set_reminder"
    ANALYZE_EFFICIENCY = "analyze_efficiency"
    CHAT_GENERAL = "chat_general"
    GET_HELP = "get_help"

@dataclass
class Intent:
    type: IntentType
    confidence: float
    entities: Dict[str, Any]
    raw_text: str

class IntentRecognizer:
    def __init__(self):
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
    async def recognize_intent(self, text: str, context: Optional[Dict] = None) -> Intent:
        """识别用户意图"""
        
        # 构建AI提示词
        prompt = self._build_intent_prompt(text, context)
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": text}
                ],
                temperature=0.1,
                max_tokens=300
            )
            
            result = response.choices[0].message.content
            return self._parse_intent_result(result, text)
            
        except Exception as e:
            logger.error(f"Intent recognition failed: {e}")
            # 降级到规则识别
            return self._rule_based_recognition(text)
    
    def _build_intent_prompt(self, text: str, context: Optional[Dict] = None) -> str:
        context_info = ""
        if context:
            context_info = f"对话上下文: {json.dumps(context, ensure_ascii=False)}\n"
        
        return f"""
你是BetterP智能生活管理助手的意图识别模块。请分析用户输入并识别意图。

{context_info}

支持的意图类型:
- create_task: 创建新任务或待办事项
- update_task: 更新现有任务状态或信息  
- query_tasks: 查询任务列表或特定任务
- delete_task: 删除任务
- schedule_meeting: 安排会议
- set_reminder: 设置提醒
- analyze_efficiency: 分析工作效率或获取优化建议
- chat_general: 一般对话聊天
- get_help: 获取帮助信息

请以JSON格式返回:
{{
    "intent": "意图类型",
    "confidence": 置信度(0-1),
    "entities": {{
        "task_title": "任务标题",
        "due_date": "截止时间",
        "priority": "优先级",
        "description": "详细描述",
        "action": "具体操作"
    }}
}}

用户输入: {text}
"""
    
    def _parse_intent_result(self, result: str, raw_text: str) -> Intent:
        """解析AI返回的意图结果"""
        try:
            data = json.loads(result)
            intent_type = IntentType(data.get('intent', 'chat_general'))
            confidence = data.get('confidence', 0.5)
            entities = data.get('entities', {})
            
            return Intent(
                type=intent_type,
                confidence=confidence,
                entities=entities,
                raw_text=raw_text
            )
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"Failed to parse intent result: {e}")
            return self._rule_based_recognition(raw_text)
    
    def _rule_based_recognition(self, text: str) -> Intent:
        """基于规则的意图识别备选方案"""
        text_lower = text.lower()
        
        # 创建任务关键词
        create_keywords = ['创建', '添加', '新建', '要做', '需要做', '安排', '计划做']
        if any(keyword in text_lower for keyword in create_keywords):
            return Intent(
                type=IntentType.CREATE_TASK,
                confidence=0.8,
                entities={'task_title': text},
                raw_text=text
            )
        
        # 查询关键词
        query_keywords = ['查看', '显示', '看看', '有什么', '列出', '今天', '明天']
        if any(keyword in text_lower for keyword in query_keywords):
            return Intent(
                type=IntentType.QUERY_TASKS,
                confidence=0.8,
                entities={},
                raw_text=text
            )
        
        # 分析关键词
        analyze_keywords = ['分析', '效率', '统计', '报告', '建议', '优化']
        if any(keyword in text_lower for keyword in analyze_keywords):
            return Intent(
                type=IntentType.ANALYZE_EFFICIENCY,
                confidence=0.8,
                entities={},
                raw_text=text
            )
        
        # 默认为一般对话
        return Intent(
            type=IntentType.CHAT_GENERAL,
            confidence=0.6,
            entities={},
            raw_text=text
        )
```

#### 3. 对话状态管理
```python
# 对话状态管理器
from datetime import datetime, timedelta
import redis
import json

@dataclass
class DialogueContext:
    session_id: str
    user_id: str
    current_intent: Optional[str] = None
    pending_entities: Dict[str, Any] = None
    conversation_history: List[Dict] = None
    last_activity: datetime = None
    
    def __post_init__(self):
        if self.pending_entities is None:
            self.pending_entities = {}
        if self.conversation_history is None:
            self.conversation_history = []
        if self.last_activity is None:
            self.last_activity = datetime.now()

class DialogueStateManager:
    def __init__(self):
        self.redis = redis.Redis.from_url(settings.REDIS_URL)
        self.context_ttl = 7200  # 2小时上下文过期
    
    async def get_context(self, session_id: str) -> Optional[DialogueContext]:
        """获取对话上下文"""
        try:
            context_data = self.redis.get(f"dialogue_context:{session_id}")
            if context_data:
                data = json.loads(context_data)
                return DialogueContext(
                    session_id=data['session_id'],
                    user_id=data['user_id'],
                    current_intent=data.get('current_intent'),
                    pending_entities=data.get('pending_entities', {}),
                    conversation_history=data.get('conversation_history', []),
                    last_activity=datetime.fromisoformat(data['last_activity'])
                )
            return None
        except Exception as e:
            logger.error(f"Failed to get dialogue context: {e}")
            return None
    
    async def save_context(self, context: DialogueContext):
        """保存对话上下文"""
        try:
            context_data = {
                'session_id': context.session_id,
                'user_id': context.user_id,
                'current_intent': context.current_intent,
                'pending_entities': context.pending_entities,
                'conversation_history': context.conversation_history[-20:],  # 只保留最近20条
                'last_activity': context.last_activity.isoformat()
            }
            
            self.redis.setex(
                f"dialogue_context:{context.session_id}",
                self.context_ttl,
                json.dumps(context_data, ensure_ascii=False)
            )
        except Exception as e:
            logger.error(f"Failed to save dialogue context: {e}")
    
    async def update_context(self, session_id: str, intent: Intent, response: str):
        """更新对话上下文"""
        context = await self.get_context(session_id)
        if context:
            # 添加到对话历史
            context.conversation_history.append({
                'timestamp': datetime.now().isoformat(),
                'user_input': intent.raw_text,
                'intent': intent.type.value,
                'entities': intent.entities,
                'bot_response': response
            })
            
            # 更新当前意图
            context.current_intent = intent.type.value
            context.last_activity = datetime.now()
            
            # 处理待填充的实体
            if intent.type in [IntentType.CREATE_TASK, IntentType.UPDATE_TASK]:
                self._update_pending_entities(context, intent)
            
            await self.save_context(context)
    
    def _update_pending_entities(self, context: DialogueContext, intent: Intent):
        """更新待填充的实体信息"""
        if intent.type == IntentType.CREATE_TASK:
            required_entities = ['task_title']
            for entity in required_entities:
                if entity in intent.entities and intent.entities[entity]:
                    context.pending_entities[entity] = intent.entities[entity]
    
    def needs_more_info(self, intent: Intent) -> Optional[str]:
        """判断是否需要更多信息"""
        if intent.type == IntentType.CREATE_TASK:
            if not intent.entities.get('task_title'):
                return "请告诉我您要创建的任务标题是什么？"
        
        return None
```

#### 4. 智能响应生成
```python
# 响应生成器
class ResponseGenerator:
    def __init__(self):
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def generate_response(self, intent: Intent, context: DialogueContext, action_result: Optional[Dict] = None) -> Dict[str, Any]:
        """生成智能响应"""
        
        # 检查是否需要更多信息
        missing_info = self._check_missing_info(intent)
        if missing_info:
            return {
                'type': 'question',
                'message': missing_info,
                'requires_input': True
            }
        
        # 根据意图类型生成不同响应
        if intent.type == IntentType.CREATE_TASK:
            return await self._handle_create_task_response(intent, action_result)
        
        elif intent.type == IntentType.QUERY_TASKS:
            return await self._handle_query_tasks_response(intent, action_result)
        
        elif intent.type == IntentType.ANALYZE_EFFICIENCY:
            return await self._handle_analysis_response(intent, action_result)
        
        elif intent.type == IntentType.CHAT_GENERAL:
            return await self._handle_general_chat(intent, context)
        
        else:
            return {
                'type': 'text',
                'message': '我已经处理了您的请求。',
                'requires_input': False
            }
    
    async def _handle_create_task_response(self, intent: Intent, action_result: Optional[Dict]) -> Dict[str, Any]:
        """处理任务创建响应"""
        if action_result and action_result.get('success'):
            task = action_result['data']
            message = f"✅ 已为您创建任务「{task['title']}」"
            
            if task.get('due_date'):
                due_date = datetime.fromisoformat(task['due_date']).strftime('%Y年%m月%d日')
                message += f"\n⏰ 截止时间：{due_date}"
            
            if task.get('priority', 3) >= 4:
                message += "\n🔥 这是一个高优先级任务，建议优先处理"
            
            return {
                'type': 'success',
                'message': message,
                'data': task,
                'requires_input': False
            }
        else:
            return {
                'type': 'error', 
                'message': '任务创建失败，请重试',
                'requires_input': False
            }
    
    async def _handle_query_tasks_response(self, intent: Intent, action_result: Optional[Dict]) -> Dict[str, Any]:
        """处理任务查询响应"""
        if action_result and action_result.get('success'):
            tasks = action_result['data']
            
            if not tasks:
                return {
                    'type': 'info',
                    'message': '🎉 太棒了！您暂时没有待处理的任务。',
                    'requires_input': False
                }
            
            # 格式化任务列表
            message = f"📋 为您找到 {len(tasks)} 个任务：\n\n"
            
            for i, task in enumerate(tasks[:10], 1):  # 最多显示10个
                status_emoji = {
                    'pending': '⏳',
                    'in_progress': '🔄', 
                    'completed': '✅'
                }.get(task['status'], '📝')
                
                priority_text = ""
                if task.get('priority', 3) >= 4:
                    priority_text = " 🔥"
                
                message += f"{i}. {status_emoji} {task['title']}{priority_text}\n"
            
            if len(tasks) > 10:
                message += f"\n... 还有 {len(tasks) - 10} 个任务"
            
            return {
                'type': 'list',
                'message': message,
                'data': tasks,
                'requires_input': False
            }
        
        return {
            'type': 'error',
            'message': '获取任务列表失败，请重试',
            'requires_input': False
        }
    
    async def _handle_general_chat(self, intent: Intent, context: DialogueContext) -> Dict[str, Any]:
        """处理一般对话"""
        
        # 构建对话上下文
        conversation_context = ""
        if context.conversation_history:
            recent_history = context.conversation_history[-3:]  # 最近3轮对话
            for item in recent_history:
                conversation_context += f"用户: {item['user_input']}\n助手: {item['bot_response']}\n"
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system", 
                        "content": """你是BetterP智能生活管理助手，专门帮助用户管理任务、提高效率。
特点：
- 友好、专业、有帮助
- 简洁明了，不冗长
- 适时提供任务管理建议
- 使用emoji让对话生动"""
                    },
                    {
                        "role": "user",
                        "content": f"对话上下文：\n{conversation_context}\n\n当前输入：{intent.raw_text}"
                    }
                ],
                temperature=0.7,
                max_tokens=200
            )
            
            message = response.choices[0].message.content
            
            return {
                'type': 'chat',
                'message': message,
                'requires_input': False
            }
            
        except Exception as e:
            logger.error(f"General chat failed: {e}")
            return {
                'type': 'chat',
                'message': '我在思考中遇到了一些问题，请稍后再试 😊',
                'requires_input': False
            }
```

---

## 📱 API接口设计

### 对话接口
```python
# WebSocket连接处理
from fastapi import WebSocket, WebSocketDisconnect
import json
import asyncio

class ChatWebSocketManager:
    def __init__(self):
        self.connections: Dict[str, WebSocket] = {}
        self.dialogue_service = DialogueService()
    
    async def connect(self, websocket: WebSocket, user_id: str, session_id: str):
        """建立WebSocket连接"""
        await websocket.accept()
        connection_id = f"{user_id}:{session_id}"
        self.connections[connection_id] = websocket
        
        # 发送欢迎消息
        welcome_msg = {
            'type': 'welcome',
            'message': '👋 欢迎使用BetterP！我是您的智能助手，可以帮您管理任务、提高效率。',
            'session_id': session_id
        }
        await websocket.send_text(json.dumps(welcome_msg, ensure_ascii=False))
    
    def disconnect(self, user_id: str, session_id: str):
        """断开连接"""
        connection_id = f"{user_id}:{session_id}"
        if connection_id in self.connections:
            del self.connections[connection_id]
    
    async def handle_message(self, websocket: WebSocket, user_id: str, session_id: str, message: Dict):
        """处理用户消息"""
        try:
            # 处理对话
            response = await self.dialogue_service.process_dialogue(
                user_id=user_id,
                session_id=session_id,
                input_data=message
            )
            
            # 发送响应
            await websocket.send_text(json.dumps(response, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            error_response = {
                'type': 'error',
                'message': '处理消息时出现错误，请重试'
            }
            await websocket.send_text(json.dumps(error_response, ensure_ascii=False))

# FastAPI WebSocket端点
@router.websocket("/chat/{user_id}/{session_id}")
async def websocket_chat(websocket: WebSocket, user_id: str, session_id: str):
    chat_manager = ChatWebSocketManager()
    await chat_manager.connect(websocket, user_id, session_id)
    
    try:
        while True:
            # 接收用户消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理消息
            await chat_manager.handle_message(websocket, user_id, session_id, message)
            
    except WebSocketDisconnect:
        chat_manager.disconnect(user_id, session_id)
        logger.info(f"WebSocket disconnected for user {user_id}, session {session_id}")

# RESTful API端点
@router.post("/chat/message", response_model=ChatResponse)
async def send_message(
    message_data: ChatMessageRequest,
    current_user: User = Depends(get_current_user),
    dialogue_service: DialogueService = Depends()
):
    """发送聊天消息 (HTTP接口)"""
    
    response = await dialogue_service.process_dialogue(
        user_id=current_user.id,
        session_id=message_data.session_id,
        input_data={
            'type': message_data.input_type,
            'content': message_data.content,
            'metadata': message_data.metadata
        }
    )
    
    return ChatResponse(**response)

@router.post("/chat/voice", response_model=ChatResponse)
async def send_voice_message(
    session_id: str = Form(...),
    audio_file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    dialogue_service: DialogueService = Depends()
):
    """发送语音消息"""
    
    if not audio_file.content_type.startswith('audio/'):
        raise HTTPException(400, "Invalid audio file")
    
    audio_data = await audio_file.read()
    
    response = await dialogue_service.process_dialogue(
        user_id=current_user.id,
        session_id=session_id,
        input_data={
            'type': 'voice',
            'content': audio_data,
            'metadata': {'filename': audio_file.filename}
        }
    )
    
    return ChatResponse(**response)
```

---

## 🎛 性能优化和成本控制

### 性能优化策略
```yaml
API响应优化:
  缓存策略:
    - 用户信息: 1小时缓存
    - 任务列表: 15分钟缓存  
    - AI推理结果: 30分钟缓存
    - 对话上下文: 实时更新，Redis存储
  
  数据库优化:
    - 索引优化: 针对常用查询建立复合索引
    - 连接池: 主库200连接，从库400连接
    - 读写分离: 读操作路由到从库
    - 慢查询监控: >100ms查询告警

AI服务优化:
  GPT调用优化:
    - 结果缓存: 相似问题复用结果
    - 批量处理: 多个请求合并处理
    - 模型选择: 简单问题使用GPT-3.5
    - Token控制: 限制输入输出长度
  
  本地模型:
    - 意图识别: 使用fine-tuned BERT模型
    - 简单分类: 本地机器学习模型
    - 情感分析: 预训练中文模型
```

### 成本预算 (月度)
```yaml
基础设施成本:
  计算资源:
    - Kubernetes集群: $3,000
    - 负载均衡器: $500
    - CDN服务: $800
    小计: $4,300

  数据库服务:
    - PostgreSQL (主从): $2,000
    - Redis集群: $800
    - MongoDB: $1,200
    小计: $4,000

AI服务成本:
  - OpenAI API调用: $8,000 (预估200万次调用)
  - 语音识别服务: $1,000
  小计: $9,000

运营成本:
  - 监控服务: $300
  - 日志服务: $500
  - 备份存储: $400
  小计: $1,200

总计: $18,500/月
单用户成本: $0.185/月 (10万用户)
```

### 扩容策略
```yaml
自动扩容配置:
  应用服务:
    - 触发条件: CPU > 70% 或 QPS > 阈值
    - 扩容步长: 每次增加2个Pod
    - 最大实例: 20个Pod
    - 缩容延迟: 5分钟无负载后缩容

  数据库扩容:
    - 读负载高: 增加从库实例
    - 存储不足: 自动扩展磁盘空间
    - 连接数高: 调整连接池配置

负载测试目标:
  - 并发用户: 8,000
  - 平均响应时间: <200ms
  - 成功率: >99.5%
  - 系统稳定运行: 24小时
```

---

## 🚀 实施计划

### 第一阶段: 核心功能开发 (6周)
```yaml
Week 1-2: 基础架构
  - 设置Kubernetes集群
  - 部署PostgreSQL、Redis、MongoDB
  - 实现用户认证服务
  - 创建基础API框架

Week 3-4: 对话控制中枢
  - 实现WebSocket连接管理
  - 开发意图识别引擎
  - 集成OpenAI GPT API
  - 实现对话状态管理

Week 5-6: 任务管理服务
  - 实现任务CRUD操作
  - 开发基础任务拆解功能
  - 集成AI引擎
  - Flutter前端基础功能
```

### 第二阶段: 高级功能 (4周)
```yaml
Week 7-8: AI功能完善
  - 实现效率分析引擎
  - 开发个性化推荐
  - 优化AI调用性能
  - 添加语音识别功能

Week 9-10: 用户体验优化
  - 完善Flutter UI
  - 实现离线功能
  - 添加通知推送
  - 性能优化和测试
```

### 第三阶段: 部署和优化 (2周)
```yaml
Week 11-12: 生产部署
  - 生产环境配置
  - 监控系统部署
  - 安全加固
  - 用户测试和反馈
```

这个方案针对10万用户规模进行了合理的简化和优化，既保证了功能完整性，又控制了复杂度和成本。对话控制中枢作为用户交互的统一入口，提供智能、自然的对话体验。
