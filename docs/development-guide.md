# BetterP 智能生活管理应用开发指南
## 10万用户规模的企业级架构方案

## 项目概述

BetterP 是一个基于三大核心引擎的智能生活管理应用，能够自动对个人行为进行归类、智能任务拆解和效率优化。

### 目标规模
- **用户规模**: 10万注册用户
- **日活用户**: 2-3万 (30%活跃率)
- **峰值并发**: 5,000-8,000用户
- **峰值QPS**: 50,000-80,000

### 核心功能
- **智能对话交互**: 对话控制中枢作为用户交互统一入口
- **智能任务拆解**: 支持简单提醒、复杂任务、依赖任务的智能分解
- **多模型AI支持**: 集成通义千问、豆包、Kimi等多个AI模型
- **效率优化**: 基于行为数据分析提供个性化优化建议

### 三大核心引擎
1. **对话控制中枢**: 用户交互统一入口，支持多模态智能对话
2. **动态任务拆解引擎**: 智能任务分析和规划系统，支持多种任务类型
3. **熵减引擎**: 效率优化智能系统

---

## 技术栈

### 前端
- **Flutter**: 跨平台移动应用开发
- **Dart**: 编程语言
- **Provider/Riverpod**: 状态管理
- **Dio**: HTTP请求库
- **speech_to_text**: 本地语音识别插件

### 后端
- **Python 3.11+**: 主要编程语言
- **FastAPI**: 高性能Web框架
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证
- **SQLAlchemy**: ORM框架
- **Alembic**: 数据库迁移

### 数据库
- **PostgreSQL**: 主数据库（用户数据、任务数据、配置数据）
- **Redis**: 缓存和会话存储

### AI/ML组件
- **多模型支持**: 通义千问、豆包(字节)、Kimi、GPT(备选)
- **本地语音识别**: iOS Speech Framework / Android SpeechRecognizer
- **scikit-learn**: 机器学习算法
- **spaCy**: 自然语言处理
- **统一AI接口**: 支持模型切换和降级机制

### 基础设施
- **Docker**: 容器化部署
- **Docker Compose**: 本地开发环境
- **Nginx**: 反向代理
- **Kubernetes**: 生产环境容器编排（可选）

---

## 系统架构

### 整体架构图 (10万用户规模)

```
                    ┌─────────────────────────────────────┐
                    │           CDN + 负载均衡              │
                    └─────────────────┬───────────────────┘
                                     │
                    ┌─────────────────┴───────────────────┐
                    │        API Gateway                  │
                    │      (Nginx + Kong)                 │
                    └─────────────────┬───────────────────┘
                                     │
        ┌────────────────────────────┼────────────────────────────┐
        │                            │                            │
┌───────┴────────┐          ┌────────┴────────┐          ┌────────┴────────┐
│   用户服务        │          │  对话控制中枢     │          │   任务服务        │
│   (User Service) │          │ (Chat Control)  │          │ (Task Service)  │
│     3 实例       │          │     5 实例       │          │     4 实例       │
└───────┬────────┘          └────────┬────────┘          └────────┬────────┘
        │                            │                            │
        │              ┌─────────────┴───────────────┐            │
        │              │      多模型AI引擎服务        │            │
        │              │   (通义千问/豆包/Kimi)       │            │
        │              │        3 实例              │            │
        │              └─────────────┬───────────────┘            │
        │                            │                            │
        └────────────────────────────┼────────────────────────────┘
                                     │
                    ┌─────────────────┴───────────────────┐
                    │         数据存储层                   │
                    │   PostgreSQL(主从) + Redis + MongoDB │
                    └─────────────────────────────────────┘
```

### 微服务架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Web 界面   │ │  Mobile App │ │   API 接口   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────┐
│                     核心服务层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │对话控制服务  │ │任务拆解服务  │ │效率优化服务  │          │
│  │             │ │             │ │             │          │
│  │• 意图识别   │ │• 任务解析   │ │• 数据采集   │          │
│  │• 对话管理   │ │• 拆解算法   │ │• 模式分析   │          │
│  │• 多模态处理 │ │• 优先级计算 │ │• 效率评估   │          │
│  │• 输出生成   │ │• 计划生成   │ │• 策略生成   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────┐
│                    支撑服务层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ 用户管理服务 │ │ 通知服务    │ │ 文件服务    │          │
│  │ 权限管理服务 │ │ 配置管理服务 │ │ 日志服务    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

---

## 数据库设计

### PostgreSQL 主数据库

#### 用户管理相关表

```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url TEXT,
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'zh-CN',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    is_premium BOOLEAN DEFAULT false
);

-- 用户配置表
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, preference_key)
);

-- 用户会话表
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    device_info JSONB,
    ip_address INET,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);
```

#### 任务管理相关表

```sql
-- 项目表
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active',
    priority INTEGER DEFAULT 3,
    start_date DATE,
    due_date DATE,
    completion_percentage INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 任务表
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
    parent_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    task_type VARCHAR(50) NOT NULL, -- project, reminder, meeting, work, life, exercise
    status VARCHAR(20) DEFAULT 'pending', -- pending, in_progress, completed, cancelled
    priority INTEGER DEFAULT 3, -- 1-5, 1 being highest
    difficulty INTEGER DEFAULT 3, -- 1-5, complexity assessment
    estimated_duration INTEGER, -- in minutes
    actual_duration INTEGER, -- in minutes
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB -- additional task-specific data
);

-- 任务依赖关系表
CREATE TABLE task_dependencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    depends_on_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    dependency_type VARCHAR(20) DEFAULT 'finish_to_start', -- finish_to_start, start_to_start, finish_to_finish, start_to_finish
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(task_id, depends_on_task_id)
);

-- 子任务表 (任务拆解结果)
CREATE TABLE task_breakdowns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    subtask_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    breakdown_level INTEGER NOT NULL, -- 拆解层级
    order_index INTEGER NOT NULL, -- 执行顺序
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 对话管理相关表

```sql
-- 对话会话表
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_name VARCHAR(255),
    context_data JSONB, -- 对话上下文
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- 对话消息表
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    message_type VARCHAR(20) NOT NULL, -- user, assistant, system
    content TEXT NOT NULL,
    metadata JSONB, -- intent, entities, confidence, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 意图识别结果表
CREATE TABLE intent_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
    intent VARCHAR(100) NOT NULL,
    confidence FLOAT NOT NULL,
    entities JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 效率监控相关表

```sql
-- 工作时间记录表
CREATE TABLE work_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    task_id UUID REFERENCES tasks(id) ON DELETE SET NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- in seconds
    focus_score FLOAT, -- 0-1, calculated focus level
    interruption_count INTEGER DEFAULT 0,
    quality_score FLOAT, -- 0-1, work quality assessment
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 效率指标表
CREATE TABLE efficiency_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    metric_date DATE NOT NULL,
    task_completion_rate FLOAT,
    average_focus_score FLOAT,
    total_productive_time INTEGER, -- in minutes
    task_switching_frequency INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, metric_date)
);

-- 优化建议表
CREATE TABLE optimization_suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    suggestion_type VARCHAR(50) NOT NULL, -- time_management, focus_improvement, habit_formation
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    priority INTEGER DEFAULT 3,
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, rejected, applied
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    applied_at TIMESTAMP WITH TIME ZONE
);
```

### Redis 缓存结构

```
用户会话: user_session:{user_id}:{session_token}
对话上下文: chat_context:{session_id}
任务缓存: task_cache:{user_id}
实时指标: realtime_metrics:{user_id}
```

### AI模型结果存储 (PostgreSQL JSONB)

```sql
-- AI模型结果表
CREATE TABLE ai_model_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    model_type VARCHAR(50) NOT NULL, -- intent_recognition, task_decomposition, efficiency_analysis
    input_data JSONB NOT NULL,
    output_data JSONB NOT NULL,
    model_version VARCHAR(20) DEFAULT '1.0',
    confidence FLOAT DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT NOW(),
    metadata JSONB
);

-- 系统日志表
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    service_name VARCHAR(50) NOT NULL, -- dialogue_control, task_decomposition, entropy_reduction
    log_level VARCHAR(20) NOT NULL, -- info, warning, error
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    additional_data JSONB
);

-- 索引优化
CREATE INDEX idx_ai_results_user_time ON ai_model_results (user_id, created_at DESC);
CREATE INDEX idx_ai_results_type ON ai_model_results (model_type);
CREATE INDEX idx_ai_results_confidence ON ai_model_results (confidence);
CREATE INDEX idx_logs_service_time ON system_logs (service_name, created_at DESC);
CREATE INDEX idx_logs_level ON system_logs (log_level, created_at DESC);
```

### 时序数据存储 (PostgreSQL)

```sql
-- 用户行为时序数据表
CREATE TABLE user_behavior_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL,
    duration INTEGER NOT NULL, -- 持续时间(分钟)
    efficiency_score FLOAT DEFAULT 0.0,
    focus_level INTEGER DEFAULT 1, -- 1-5
    recorded_at TIMESTAMP DEFAULT NOW(),
    metadata JSONB
);

-- 系统性能监控表
CREATE TABLE system_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_name VARCHAR(50) NOT NULL,
    instance_id VARCHAR(50) NOT NULL,
    cpu_usage FLOAT NOT NULL,
    memory_usage FLOAT NOT NULL,
    response_time INTEGER NOT NULL, -- 响应时间(ms)
    recorded_at TIMESTAMP DEFAULT NOW()
);

-- 时序数据索引优化
CREATE INDEX idx_behavior_user_time ON user_behavior_metrics (user_id, recorded_at DESC);
CREATE INDEX idx_behavior_activity ON user_behavior_metrics (activity_type, recorded_at DESC);
CREATE INDEX idx_system_service_time ON system_performance_metrics (service_name, recorded_at DESC);

-- 时间分区优化 (可选)
CREATE TABLE user_behavior_metrics_y2024m01 PARTITION OF user_behavior_metrics
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

---

## 通信架构设计

### 整体通信方案

采用 **本地语音识别 + SSE + HTTP** 的简化架构：

#### 客户端到服务端
- **语音输入**: 本地识别转文本 → HTTP POST
- **文字输入**: HTTP POST  
- **图片上传**: HTTP POST multipart
- **常规操作**: HTTP REST API

#### 服务端到客户端  
- **AI回答流式输出**: Server-Sent Events (SSE)
- **任务状态推送**: SSE
- **实时通知**: SSE
- **文件下载**: HTTP GET

### 语音处理流程

```
用户语音 → 本地识别(iOS/Android) → 文本 → HTTP POST → AI处理 → SSE推送结果
```

### 技术优势

- **简化架构**: 无需WebSocket复杂连接管理
- **隐私保护**: 语音数据不上传服务器
- **离线支持**: 本地语音识别支持离线使用
- **成本节省**: 无语音识别API费用
- **高可靠性**: HTTP + SSE 更稳定可靠

---

## API 规范

### 认证与授权

所有API端点都需要JWT token认证（除了登录和注册接口）。

```python
# 请求头
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### 统一响应格式

```python
# 成功响应
{
    "success": true,
    "data": {...},
    "message": "操作成功",
    "timestamp": "2024-01-01T00:00:00Z"
}

# 错误响应
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "参数验证失败",
        "details": {...}
    },
    "timestamp": "2024-01-01T00:00:00Z"
}
```

### 核心API端点

#### 用户管理API

```python
# 用户注册
POST /api/v1/auth/register
{
    "username": "user123",
    "email": "<EMAIL>", 
    "password": "password123",
    "full_name": "用户姓名"
}

# 用户登录
POST /api/v1/auth/login
{
    "username": "user123",
    "password": "password123"
}

# 获取用户信息
GET /api/v1/users/profile

# 更新用户信息
PUT /api/v1/users/profile
{
    "full_name": "新姓名",
    "timezone": "Asia/Shanghai",
    "language": "zh-CN"
}
```

#### 对话控制API

```python
# 发送消息
POST /api/v1/chat/message
{
    "session_id": "uuid", # 可选，新会话时不传
    "message": "帮我安排明天的工作计划",
    "message_type": "text", # text, voice, image
    "metadata": {}
}

# 获取对话历史
GET /api/v1/chat/sessions/{session_id}/messages?limit=50&offset=0

# SSE实时通信
GET /api/v1/chat/stream/{user_id}
Accept: text/event-stream

# 事件类型:
# - ai_response: AI回答流式输出  
# - task_update: 任务状态更新
# - notification: 系统通知
```

#### 任务管理API

```python
# 创建任务
POST /api/v1/tasks
{
    "title": "完成项目报告",
    "description": "编写月度项目进展报告",
    "task_type": "work",
    "priority": 4,
    "due_date": "2024-01-15T18:00:00Z",
    "project_id": "uuid" # 可选
}

# 获取任务列表
GET /api/v1/tasks?status=pending&task_type=work&limit=20&offset=0

# 任务拆解
POST /api/v1/tasks/{task_id}/decompose
{
    "decomposition_level": 2, # 拆解层级
    "auto_schedule": true # 是否自动安排时间
}

# 更新任务状态
PUT /api/v1/tasks/{task_id}/status
{
    "status": "completed",
    "completion_notes": "任务完成备注"
}
```

#### 效率分析API

```python
# 获取效率分析报告
GET /api/v1/analytics/efficiency?period=week&start_date=2024-01-01

# 获取优化建议
GET /api/v1/analytics/suggestions?category=time_management

# 记录工作会话
POST /api/v1/analytics/work-sessions
{
    "task_id": "uuid",
    "start_time": "2024-01-01T09:00:00Z",
    "end_time": "2024-01-01T10:30:00Z",
    "focus_score": 0.85,
    "interruption_count": 2
}
```

---

## Flutter 前端架构

### 项目结构

```
lib/
├── main.dart
├── app/
│   ├── app.dart
│   ├── routes.dart
│   └── theme.dart
├── core/
│   ├── constants/
│   ├── utils/
│   ├── exceptions/
│   └── extensions/
├── data/
│   ├── datasources/
│   │   ├── remote/
│   │   └── local/
│   ├── models/
│   ├── repositories/
│   └── services/
├── domain/
│   ├── entities/
│   ├── repositories/
│   └── usecases/
├── presentation/
│   ├── pages/
│   ├── widgets/
│   ├── providers/
│   └── controllers/
└── config/
    ├── environment.dart
    └── dependencies.dart
```

### 主要依赖包

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # 状态管理
  flutter_riverpod: ^2.4.9
  
  # 网络请求
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # 本地存储
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # UI组件
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.1
  
  # 语音相关
  speech_to_text: ^6.6.0
  flutter_tts: ^3.8.3
  
  # 图表和数据可视化
  fl_chart: ^0.65.0
  
  # 其他工具
  uuid: ^4.2.2
  intl: ^0.19.0
  equatable: ^2.0.5
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # 代码生成
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1
```

### 核心组件设计

#### 1. 数据模型

```dart
// lib/data/models/user_model.dart
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends Equatable {
  final String id;
  final String username;
  final String email;
  final String? fullName;
  final String? avatarUrl;
  final String timezone;
  final String language;
  final bool isActive;
  final bool isPremium;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.username,
    required this.email,
    this.fullName,
    this.avatarUrl,
    required this.timezone,
    required this.language,
    required this.isActive,
    required this.isPremium,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        fullName,
        avatarUrl,
        timezone,
        language,
        isActive,
        isPremium,
        createdAt,
        updatedAt,
      ];
}

// lib/data/models/task_model.dart
@JsonSerializable()
class TaskModel extends Equatable {
  final String id;
  final String userId;
  final String? projectId;
  final String? parentTaskId;
  final String title;
  final String? description;
  final TaskType taskType;
  final TaskStatus status;
  final int priority;
  final int difficulty;
  final int? estimatedDuration;
  final int? actualDuration;
  final DateTime? dueDate;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  const TaskModel({
    required this.id,
    required this.userId,
    this.projectId,
    this.parentTaskId,
    required this.title,
    this.description,
    required this.taskType,
    required this.status,
    required this.priority,
    required this.difficulty,
    this.estimatedDuration,
    this.actualDuration,
    this.dueDate,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  factory TaskModel.fromJson(Map<String, dynamic> json) =>
      _$TaskModelFromJson(json);

  Map<String, dynamic> toJson() => _$TaskModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        projectId,
        parentTaskId,
        title,
        description,
        taskType,
        status,
        priority,
        difficulty,
        estimatedDuration,
        actualDuration,
        dueDate,
        completedAt,
        createdAt,
        updatedAt,
        metadata,
      ];
}

enum TaskType { project, reminder, meeting, work, life, exercise }

enum TaskStatus { pending, inProgress, completed, cancelled }
```

#### 2. API服务

```dart
// lib/data/services/api_service.dart
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/user_model.dart';
import '../models/task_model.dart';
import '../models/chat_message_model.dart';

part 'api_service.g.dart';

@RestApi(baseUrl: "http://localhost:8000/api/v1/")
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  // 认证API
  @POST("/auth/register")
  Future<ApiResponse<UserModel>> register(@Body() Map<String, dynamic> data);

  @POST("/auth/login")
  Future<ApiResponse<Map<String, dynamic>>> login(@Body() Map<String, dynamic> data);

  // 用户API
  @GET("/users/profile")
  Future<ApiResponse<UserModel>> getProfile();

  @PUT("/users/profile")
  Future<ApiResponse<UserModel>> updateProfile(@Body() Map<String, dynamic> data);

  // 任务API
  @GET("/tasks")
  Future<ApiResponse<List<TaskModel>>> getTasks(@Queries() Map<String, dynamic> queries);

  @POST("/tasks")
  Future<ApiResponse<TaskModel>> createTask(@Body() Map<String, dynamic> data);

  @PUT("/tasks/{id}")
  Future<ApiResponse<TaskModel>> updateTask(@Path("id") String id, @Body() Map<String, dynamic> data);

  @DELETE("/tasks/{id}")
  Future<ApiResponse<void>> deleteTask(@Path("id") String id);

  @POST("/tasks/{id}/decompose")
  Future<ApiResponse<List<TaskModel>>> decomposeTask(@Path("id") String id, @Body() Map<String, dynamic> data);

  // 对话API
  @POST("/chat/message")
  Future<ApiResponse<ChatMessageModel>> sendMessage(@Body() Map<String, dynamic> data);

  @GET("/chat/sessions/{sessionId}/messages")
  Future<ApiResponse<List<ChatMessageModel>>> getChatMessages(@Path("sessionId") String sessionId);
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final ApiError? error;
  final DateTime timestamp;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    required this.timestamp,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(Object? json) fromJsonT) {
    return ApiResponse<T>(
      success: json['success'],
      data: json['data'] != null ? fromJsonT(json['data']) : null,
      message: json['message'],
      error: json['error'] != null ? ApiError.fromJson(json['error']) : null,
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

class ApiError {
  final String code;
  final String message;
  final Map<String, dynamic>? details;

  ApiError({
    required this.code,
    required this.message,
    this.details,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      code: json['code'],
      message: json['message'],
      details: json['details'],
    );
  }
}
```

#### 3. 状态管理

```dart
// lib/presentation/providers/auth_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/user_model.dart';
import '../../data/services/api_service.dart';
import '../../core/utils/storage_service.dart';

class AuthState {
  final UserModel? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  AuthState copyWith({
    UserModel? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

class AuthNotifier extends StateNotifier<AuthState> {
  final ApiService _apiService;
  final StorageService _storageService;

  AuthNotifier(this._apiService, this._storageService) : super(AuthState());

  Future<void> login(String username, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final response = await _apiService.login({
        'username': username,
        'password': password,
      });

      if (response.success && response.data != null) {
        final token = response.data!['access_token'];
        final user = UserModel.fromJson(response.data!['user']);

        await _storageService.saveToken(token);
        state = state.copyWith(
          user: user,
          isAuthenticated: true,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.error?.message ?? '登录失败',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  Future<void> logout() async {
    await _storageService.clearToken();
    state = AuthState();
  }

  Future<void> checkAuthStatus() async {
    final token = await _storageService.getToken();
    if (token != null) {
      try {
        final response = await _apiService.getProfile();
        if (response.success && response.data != null) {
          state = state.copyWith(
            user: response.data,
            isAuthenticated: true,
          );
        }
      } catch (e) {
        await _storageService.clearToken();
      }
    }
  }
}

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final apiService = ref.read(apiServiceProvider);
  final storageService = ref.read(storageServiceProvider);
  return AuthNotifier(apiService, storageService);
});
```

#### 4. 主界面设计

```dart
// lib/presentation/pages/home_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/task_card.dart';
import '../widgets/efficiency_chart.dart';
import '../widgets/chat_bubble.dart';

class HomePage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    final taskState = ref.watch(taskProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('BetterP'),
        actions: [
          IconButton(
            onPressed: () => _showNotifications(context),
            icon: Icon(Icons.notifications),
          ),
          IconButton(
            onPressed: () => _showProfile(context),
            icon: CircleAvatar(
              backgroundImage: authState.user?.avatarUrl != null
                  ? NetworkImage(authState.user!.avatarUrl!)
                  : null,
              child: authState.user?.avatarUrl == null
                  ? Icon(Icons.person)
                  : null,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 快速操作区域
            _buildQuickActions(context, ref),
            
            SizedBox(height: 20),
            
            // 今日任务
            _buildTodayTasks(context, ref),
            
            SizedBox(height: 20),
            
            // 效率分析
            _buildEfficiencyAnalysis(context, ref),
            
            SizedBox(height: 20),
            
            // 智能建议
            _buildSmartSuggestions(context, ref),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showChatDialog(context),
        child: Icon(Icons.chat),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '快速操作',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickActionButton(
                  icon: Icons.add_task,
                  label: '添加任务',
                  onTap: () => _showAddTaskDialog(context),
                ),
                _buildQuickActionButton(
                  icon: Icons.schedule,
                  label: '智能规划',
                  onTap: () => _triggerSmartPlanning(ref),
                ),
                _buildQuickActionButton(
                  icon: Icons.analytics,
                  label: '效率分析',
                  onTap: () => _showEfficiencyReport(context),
                ),
                _buildQuickActionButton(
                  icon: Icons.mic,
                  label: '语音输入',
                  onTap: () => _startVoiceInput(context),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Column(
          children: [
            Icon(icon, size: 32),
            SizedBox(height: 8),
            Text(label, style: TextStyle(fontSize: 12)),
          ],
        ),
      ),
    );
  }

  Widget _buildTodayTasks(BuildContext context, WidgetRef ref) {
    final taskState = ref.watch(taskProvider);
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '今日任务',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                TextButton(
                  onPressed: () => _showAllTasks(context),
                  child: Text('查看全部'),
                ),
              ],
            ),
            SizedBox(height: 12),
            if (taskState.isLoading)
              Center(child: CircularProgressIndicator())
            else if (taskState.todayTasks.isEmpty)
              Center(
                child: Text('今天没有待办任务'),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: taskState.todayTasks.length,
                itemBuilder: (context, index) {
                  final task = taskState.todayTasks[index];
                  return TaskCard(
                    task: task,
                    onTap: () => _showTaskDetail(context, task),
                    onStatusChanged: (status) => _updateTaskStatus(ref, task, status),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  // 其他方法实现...
}
```

---

## Python 后端实现

### 项目结构

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── security.py
│   │   └── exceptions.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── deps.py
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── auth.py
│   │       ├── users.py
│   │       ├── tasks.py
│   │       ├── chat.py
│   │       └── analytics.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── task.py
│   │   ├── chat.py
│   │   └── analytics.py
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── task.py
│   │   ├── chat.py
│   │   └── analytics.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth_service.py
│   │   ├── task_service.py
│   │   ├── chat_service.py
│   │   └── analytics_service.py
│   ├── engines/
│   │   ├── __init__.py
│   │   ├── dialogue_control/
│   │   │   ├── __init__.py
│   │   │   ├── intent_recognition.py
│   │   │   ├── dialogue_manager.py
│   │   │   └── response_generator.py
│   │   ├── task_decomposition/
│   │   │   ├── __init__.py
│   │   │   ├── task_parser.py
│   │   │   ├── decomposition_engine.py
│   │   │   └── priority_calculator.py
│   │   └── entropy_reduction/
│   │       ├── __init__.py
│   │       ├── data_collector.py
│   │       ├── pattern_analyzer.py
│   │       └── optimization_engine.py
│   └── utils/
│       ├── __init__.py
│       ├── logger.py
│       └── helpers.py
├── alembic/
│   ├── versions/
│   ├── env.py
│   └── alembic.ini
├── tests/
├── requirements.txt
├── Dockerfile
└── docker-compose.yml
```

### 核心配置

```python
# app/core/config.py
from pydantic import BaseSettings, validator
from typing import Optional, List
import secrets

class Settings(BaseSettings):
    # 基础配置
    PROJECT_NAME: str = "BetterP"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    
    # 数据库配置
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: int = 5432
    POSTGRES_USER: str = "betterp"
    POSTGRES_PASSWORD: str = "password"
    POSTGRES_DB: str = "betterp"
    
    @property
    def DATABASE_URL(self) -> str:
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    
    @property
    def REDIS_URL(self) -> str:
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # JWT配置
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 30  # 30 days
    
    # AI服务配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    
    # 跨域配置
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://localhost:8080",
    ]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # 环境配置
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 数据库连接

```python
# app/core/database.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import redis
from .config import settings

# PostgreSQL
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {},
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Redis
redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_redis():
    return redis_client

```

### 数据模型

```python
# app/models/user.py
from sqlalchemy import Column, String, Boolean, DateTime, Text
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
import uuid
from ..core.database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    avatar_url = Column(Text, nullable=True)
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="zh-CN")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    is_premium = Column(Boolean, default=False)

# app/models/task.py  
from sqlalchemy import Column, String, Integer, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from ..core.database import Base

class Project(Base):
    __tablename__ = "projects"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(String(20), default="active")
    priority = Column(Integer, default=3)
    start_date = Column(DateTime(timezone=True), nullable=True)
    due_date = Column(DateTime(timezone=True), nullable=True)
    completion_percentage = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tasks = relationship("Task", back_populates="project", cascade="all, delete-orphan")
    user = relationship("User", back_populates="projects")

class Task(Base):
    __tablename__ = "tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="SET NULL"), nullable=True)
    parent_task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id", ondelete="CASCADE"), nullable=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    task_type = Column(String(50), nullable=False)  # project, reminder, meeting, work, life, exercise
    status = Column(String(20), default="pending")  # pending, in_progress, completed, cancelled
    priority = Column(Integer, default=3)  # 1-5
    difficulty = Column(Integer, default=3)  # 1-5
    estimated_duration = Column(Integer, nullable=True)  # minutes
    actual_duration = Column(Integer, nullable=True)  # minutes
    due_date = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    metadata = Column(JSONB, nullable=True)

    # 关系
    user = relationship("User", back_populates="tasks")
    project = relationship("Project", back_populates="tasks")
    parent_task = relationship("Task", remote_side=[id], back_populates="subtasks")
    subtasks = relationship("Task", back_populates="parent_task", cascade="all, delete-orphan")
    dependencies = relationship("TaskDependency", foreign_keys="TaskDependency.task_id", back_populates="task")
    dependents = relationship("TaskDependency", foreign_keys="TaskDependency.depends_on_task_id", back_populates="depends_on_task")

class TaskDependency(Base):
    __tablename__ = "task_dependencies"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id", ondelete="CASCADE"), nullable=False)
    depends_on_task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id", ondelete="CASCADE"), nullable=False)
    dependency_type = Column(String(20), default="finish_to_start")
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    task = relationship("Task", foreign_keys=[task_id], back_populates="dependencies")
    depends_on_task = relationship("Task", foreign_keys=[depends_on_task_id], back_populates="dependents")
```

### API端点实现

```python
# app/api/v1/auth.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import timedelta
from ...core.database import get_db
from ...core.security import create_access_token, verify_password, get_password_hash
from ...core.config import settings
from ...models.user import User
from ...schemas.user import UserCreate, UserResponse, Token

router = APIRouter()

@router.post("/register", response_model=UserResponse)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    # 检查用户是否已存在
    existing_user = db.query(User).filter(
        (User.username == user_data.username) | (User.email == user_data.email)
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already registered"
        )
    
    # 创建新用户
    password_hash = get_password_hash(user_data.password)
    db_user = User(
        username=user_data.username,
        email=user_data.email,
        password_hash=password_hash,
        full_name=user_data.full_name,
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = db.query(User).filter(User.username == form_data.username).first()
    
    if not user or not verify_password(form_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user
    }

# app/api/v1/tasks.py
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from uuid import UUID
from ...core.database import get_db
from ...models.user import User
from ...models.task import Task, Project
from ...schemas.task import TaskCreate, TaskUpdate, TaskResponse, TaskDecompositionRequest
from ...services.task_service import TaskService
from ...engines.task_decomposition.decomposition_engine import TaskDecompositionEngine
from ..deps import get_current_user

router = APIRouter()

@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    status: Optional[str] = Query(None),
    task_type: Optional[str] = Query(None),
    project_id: Optional[UUID] = Query(None),
    limit: int = Query(20, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    task_service = TaskService(db)
    tasks = task_service.get_user_tasks(
        user_id=current_user.id,
        status=status,
        task_type=task_type,
        project_id=project_id,
        limit=limit,
        offset=offset
    )
    return tasks

@router.post("/", response_model=TaskResponse)
async def create_task(
    task_data: TaskCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    task_service = TaskService(db)
    task = task_service.create_task(user_id=current_user.id, task_data=task_data)
    return task

@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: UUID,
    task_data: TaskUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    task_service = TaskService(db)
    task = task_service.get_task(task_id=task_id, user_id=current_user.id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    updated_task = task_service.update_task(task=task, task_data=task_data)
    return updated_task

@router.post("/{task_id}/decompose", response_model=List[TaskResponse])
async def decompose_task(
    task_id: UUID,
    decomposition_data: TaskDecompositionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    task_service = TaskService(db)
    task = task_service.get_task(task_id=task_id, user_id=current_user.id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # 使用任务拆解引擎
    decomposition_engine = TaskDecompositionEngine()
    subtasks = await decomposition_engine.decompose_task(
        task=task,
        decomposition_level=decomposition_data.decomposition_level,
        auto_schedule=decomposition_data.auto_schedule
    )
    
    # 保存拆解结果
    created_subtasks = []
    for subtask_data in subtasks:
        subtask = task_service.create_task(
            user_id=current_user.id,
            task_data=subtask_data,
            parent_task_id=task_id
        )
        created_subtasks.append(subtask)
    
    return created_subtasks
```

## 多AI模型统一接口设计

### AI模型适配器架构

```python
# app/engines/ai_models/base.py
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class AIModelType(Enum):
    TONGYI_QIANWEN = "tongyi_qianwen"  # 通义千问
    DOUBAO = "doubao"                  # 豆包 (字节)
    KIMI = "kimi"                      # Kimi
    GPT = "openai_gpt"                 # OpenAI GPT (备选)

@dataclass
class TaskDecompositionRequest:
    task_title: str
    task_description: Optional[str]
    task_type: str  # 'reminder', 'complex', 'project'
    user_context: Dict[str, Any]
    decomposition_level: int = 2

@dataclass  
class TaskDecompositionResult:
    task_type: str  # 'simple_reminder', 'complex_task', 'sequential_task'
    subtasks: List['SubTask']
    dependencies: List['TaskDependency']
    estimated_duration: int  # 总时长(分钟)
    complexity_score: float  # 复杂度评分 0-1
    confidence: float  # AI结果置信度

@dataclass
class SubTask:
    title: str
    description: Optional[str]
    task_type: str
    priority: int  # 1-5
    estimated_duration: int  # 分钟
    order_index: int
    is_parallel: bool = False  # 是否可并行执行

@dataclass
class TaskDependency:
    task_index: int
    depends_on_index: int
    dependency_type: str  # 'finish_to_start', 'start_to_start'

class BaseAIModel(ABC):
    """AI模型基类"""
    
    @abstractmethod
    async def decompose_task(self, request: TaskDecompositionRequest) -> TaskDecompositionResult:
        """任务分解接口"""
        pass
    
    @abstractmethod
    async def chat_completion(self, messages: List[Dict], **kwargs) -> str:
        """对话完成接口"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        pass
```

### 多模型实现

```python
# app/engines/ai_models/tongyi_qianwen.py
import dashscope
from .base import BaseAIModel, TaskDecompositionRequest, TaskDecompositionResult

class TongyiQianwenModel(BaseAIModel):
    def __init__(self, api_key: str):
        self.api_key = api_key
        dashscope.api_key = api_key
    
    async def decompose_task(self, request: TaskDecompositionRequest) -> TaskDecompositionResult:
        """使用通义千问进行任务分解"""
        
        prompt = self._build_decomposition_prompt(request)
        
        try:
            response = dashscope.Generation.call(
                model='qwen-max',
                prompt=prompt,
                temperature=0.1,
                max_tokens=2000,
                result_format='message'
            )
            
            if response.status_code == HTTPStatus.OK:
                result_text = response.output.text
                return self._parse_decomposition_result(result_text, request)
            else:
                raise Exception(f"通义千问API调用失败: {response.message}")
                
        except Exception as e:
            logger.error(f"通义千问任务分解失败: {e}")
            return self._create_fallback_result(request)
    
    def _build_decomposition_prompt(self, request: TaskDecompositionRequest) -> str:
        return f"""
你是BetterP智能任务管理系统的任务分解专家。请将用户任务智能分解为可执行的子任务。

任务信息:
- 标题: {request.task_title}
- 描述: {request.task_description or "无"}
- 类型: {request.task_type}
- 用户上下文: {request.user_context}

分解要求:
1. 根据任务复杂度判断任务类型：
   - simple_reminder: 简单提醒任务(如:"下午3点开会")
   - complex_task: 复杂任务需拆分但无严格顺序(如:"准备演讲")  
   - sequential_task: 有严格前后顺序的任务(如:"开发新功能")

2. 拆分原则:
   - 每个子任务应该是原子化、可执行的
   - 估算每个子任务的时间(分钟)
   - 设定合理的优先级(1-5，1最高)
   - 识别任务间的依赖关系

3. 输出格式(严格JSON):
{{
    "task_type": "simple_reminder|complex_task|sequential_task",
    "subtasks": [
        {{
            "title": "子任务标题",
            "description": "子任务描述", 
            "task_type": "work|meeting|reminder|life",
            "priority": 优先级(1-5),
            "estimated_duration": 估算时长(分钟),
            "order_index": 执行顺序(从0开始),
            "is_parallel": 是否可并行执行(true/false)
        }}
    ],
    "dependencies": [
        {{
            "task_index": 任务索引,
            "depends_on_index": 依赖的任务索引,
            "dependency_type": "finish_to_start|start_to_start"
        }}
    ],
    "estimated_duration": 总估算时长(分钟),
    "complexity_score": 复杂度评分(0-1),
    "confidence": 结果置信度(0-1)
}}

请返回有效的JSON格式，不要包含其他说明文字。
"""

# app/engines/ai_models/doubao.py  
from volcenginesdkarkruntime import Ark
from .base import BaseAIModel, TaskDecompositionRequest, TaskDecompositionResult

class DoubaoModel(BaseAIModel):
    def __init__(self, api_key: str, endpoint: str):
        self.client = Ark(
            api_key=api_key,
            base_url=endpoint
        )
    
    async def decompose_task(self, request: TaskDecompositionRequest) -> TaskDecompositionResult:
        """使用豆包进行任务分解"""
        
        prompt = self._build_decomposition_prompt(request)
        
        try:
            response = await self.client.chat.completions.create(
                model="ep-20240611185-8h5k5",  # 豆包模型ID
                messages=[
                    {"role": "system", "content": "你是专业的任务分解专家"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            result_text = response.choices[0].message.content
            return self._parse_decomposition_result(result_text, request)
            
        except Exception as e:
            logger.error(f"豆包任务分解失败: {e}")
            return self._create_fallback_result(request)

# app/engines/ai_models/kimi.py
import openai
from .base import BaseAIModel, TaskDecompositionRequest, TaskDecompositionResult

class KimiModel(BaseAIModel):
    def __init__(self, api_key: str):
        self.client = openai.AsyncOpenAI(
            api_key=api_key,
            base_url="https://api.moonshot.cn/v1",
        )
    
    async def decompose_task(self, request: TaskDecompositionRequest) -> TaskDecompositionResult:
        """使用Kimi进行任务分解"""
        
        prompt = self._build_decomposition_prompt(request)
        
        try:
            response = await self.client.chat.completions.create(
                model="moonshot-v1-8k",
                messages=[
                    {"role": "system", "content": "你是BetterP的智能任务分解助手"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            result_text = response.choices[0].message.content
            return self._parse_decomposition_result(result_text, request)
            
        except Exception as e:
            logger.error(f"Kimi任务分解失败: {e}")
            return self._create_fallback_result(request)
```

### AI模型管理器

```python
# app/engines/ai_models/model_manager.py
from typing import Dict, List, Optional
import asyncio
import json
from .base import BaseAIModel, AIModelType, TaskDecompositionRequest, TaskDecompositionResult
from .tongyi_qianwen import TongyiQianwenModel
from .doubao import DoubaoModel  
from .kimi import KimiModel

class AIModelManager:
    """AI模型管理器 - 统一接口、智能路由、降级处理"""
    
    def __init__(self):
        self.models: Dict[AIModelType, BaseAIModel] = {}
        self.model_priority = [
            AIModelType.TONGYI_QIANWEN,  # 主要模型
            AIModelType.KIMI,            # 备选模型1  
            AIModelType.DOUBAO,          # 备选模型2
            AIModelType.GPT              # 最后备选
        ]
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化所有AI模型"""
        
        # 通义千问
        if settings.TONGYI_QIANWEN_API_KEY:
            self.models[AIModelType.TONGYI_QIANWEN] = TongyiQianwenModel(
                api_key=settings.TONGYI_QIANWEN_API_KEY
            )
        
        # 豆包
        if settings.DOUBAO_API_KEY:
            self.models[AIModelType.DOUBAO] = DoubaoModel(
                api_key=settings.DOUBAO_API_KEY,
                endpoint=settings.DOUBAO_ENDPOINT
            )
        
        # Kimi
        if settings.KIMI_API_KEY:
            self.models[AIModelType.KIMI] = KimiModel(
                api_key=settings.KIMI_API_KEY
            )
    
    async def decompose_task_with_fallback(self, request: TaskDecompositionRequest) -> TaskDecompositionResult:
        """带降级机制的任务分解"""
        
        last_exception = None
        
        for model_type in self.model_priority:
            if model_type not in self.models:
                continue
                
            try:
                logger.info(f"尝试使用 {model_type.value} 进行任务分解")
                
                model = self.models[model_type]
                result = await asyncio.wait_for(
                    model.decompose_task(request),
                    timeout=30.0  # 30秒超时
                )
                
                # 验证结果质量
                if self._validate_result(result):
                    logger.info(f"任务分解成功，使用模型: {model_type.value}")
                    return result
                else:
                    logger.warning(f"{model_type.value} 结果质量不佳，尝试下一个模型")
                    continue
                    
            except asyncio.TimeoutError:
                logger.warning(f"{model_type.value} 请求超时，尝试下一个模型")
                continue
            except Exception as e:
                logger.error(f"{model_type.value} 调用失败: {e}")
                last_exception = e
                continue
        
        # 所有模型都失败，返回规则化降级结果
        logger.error("所有AI模型调用失败，使用规则化降级")
        return self._rule_based_decomposition(request)
    
    def _validate_result(self, result: TaskDecompositionResult) -> bool:
        """验证AI返回结果的质量"""
        
        # 基础验证
        if not result or not result.subtasks:
            return False
        
        # 检查必需字段
        for subtask in result.subtasks:
            if not subtask.title or subtask.estimated_duration <= 0:
                return False
        
        # 检查依赖关系的合理性
        for dep in result.dependencies:
            if dep.task_index >= len(result.subtasks) or dep.depends_on_index >= len(result.subtasks):
                return False
        
        # 检查置信度
        if result.confidence < 0.5:
            return False
        
        return True
    
    def _rule_based_decomposition(self, request: TaskDecompositionRequest) -> TaskDecompositionResult:
        """规则化任务分解降级方案"""
        
        # 根据关键词判断任务类型
        title_lower = request.task_title.lower()
        
        # 简单提醒任务关键词
        reminder_keywords = ['提醒', '通知', '记得', '别忘', '会议', '约会']
        if any(keyword in title_lower for keyword in reminder_keywords):
            return TaskDecompositionResult(
                task_type='simple_reminder',
                subtasks=[SubTask(
                    title=request.task_title,
                    description=request.task_description,
                    task_type='reminder',
                    priority=3,
                    estimated_duration=5,
                    order_index=0
                )],
                dependencies=[],
                estimated_duration=5,
                complexity_score=0.1,
                confidence=0.6
            )
        
        # 复杂任务关键词  
        complex_keywords = ['项目', '开发', '设计', '准备', '整理', '分析']
        if any(keyword in title_lower for keyword in complex_keywords):
            subtasks = [
                SubTask(
                    title=f"准备阶段 - {request.task_title}",
                    description="收集资料和制定计划",
                    task_type='work',
                    priority=4,
                    estimated_duration=60,
                    order_index=0
                ),
                SubTask(
                    title=f"执行阶段 - {request.task_title}",
                    description="具体实施任务内容",
                    task_type='work', 
                    priority=4,
                    estimated_duration=120,
                    order_index=1
                ),
                SubTask(
                    title=f"检查完善 - {request.task_title}",
                    description="检查结果并完善细节",
                    task_type='work',
                    priority=3,
                    estimated_duration=30,
                    order_index=2
                )
            ]
            
            dependencies = [
                TaskDependency(task_index=1, depends_on_index=0, dependency_type='finish_to_start'),
                TaskDependency(task_index=2, depends_on_index=1, dependency_type='finish_to_start')
            ]
            
            return TaskDecompositionResult(
                task_type='sequential_task',
                subtasks=subtasks,
                dependencies=dependencies,
                estimated_duration=210,
                complexity_score=0.7,
                confidence=0.6
            )
        
        # 默认复杂任务
        return TaskDecompositionResult(
            task_type='complex_task',
            subtasks=[
                SubTask(
                    title=f"分析任务 - {request.task_title}",
                    description="详细分析任务需求",
                    task_type='work',
                    priority=3,
                    estimated_duration=30,
                    order_index=0
                ),
                SubTask(
                    title=f"执行任务 - {request.task_title}",
                    description="执行具体任务内容",
                    task_type='work',
                    priority=3,
                    estimated_duration=90,
                    order_index=1,
                    is_parallel=True
                )
            ],
            dependencies=[],
            estimated_duration=120,
            complexity_score=0.5,
            confidence=0.6
        )

# 全局模型管理器实例
ai_model_manager = AIModelManager()
```

### 标准化任务分解服务

```python
# app/services/task_decomposition_service.py
from typing import List, Dict, Any, Optional
from ..engines.ai_models.model_manager import ai_model_manager
from ..engines.ai_models.base import TaskDecompositionRequest, TaskDecompositionResult
from ..models.task import Task
from ..schemas.task import TaskCreate

class TaskDecompositionService:
    """统一的任务分解服务"""
    
    async def decompose_task(
        self, 
        task_title: str, 
        task_description: Optional[str] = None,
        user_context: Dict[str, Any] = None,
        decomposition_level: int = 2
    ) -> Dict[str, Any]:
        """统一的任务分解接口"""
        
        # 构建分解请求
        request = TaskDecompositionRequest(
            task_title=task_title,
            task_description=task_description,
            task_type=self._infer_task_type(task_title, task_description),
            user_context=user_context or {},
            decomposition_level=decomposition_level
        )
        
        # 使用AI模型进行分解
        result = await ai_model_manager.decompose_task_with_fallback(request)
        
        # 转换为标准格式
        return self._format_decomposition_result(result)
    
    def _infer_task_type(self, title: str, description: Optional[str]) -> str:
        """推断任务类型"""
        text = f"{title} {description or ''}".lower()
        
        if any(keyword in text for keyword in ['会议', '电话', '约会', '面试']):
            return 'meeting'
        elif any(keyword in text for keyword in ['提醒', '记得', '通知']):
            return 'reminder'  
        elif any(keyword in text for keyword in ['项目', '开发', '设计']):
            return 'project'
        elif any(keyword in text for keyword in ['运动', '健身', '锻炼']):
            return 'exercise'
        elif any(keyword in text for keyword in ['购物', '家务', '个人']):
            return 'life'
        else:
            return 'work'
    
    def _format_decomposition_result(self, result: TaskDecompositionResult) -> Dict[str, Any]:
        """格式化分解结果为前端可用格式"""
        
        return {
            'success': True,
            'task_type': result.task_type,
            'subtasks': [
                {
                    'title': subtask.title,
                    'description': subtask.description,
                    'task_type': subtask.task_type,
                    'priority': subtask.priority,
                    'estimated_duration': subtask.estimated_duration,
                    'order_index': subtask.order_index,
                    'is_parallel': subtask.is_parallel
                }
                for subtask in result.subtasks
            ],
            'dependencies': [
                {
                    'task_index': dep.task_index,
                    'depends_on_index': dep.depends_on_index,
                    'dependency_type': dep.dependency_type
                }
                for dep in result.dependencies
            ],
            'metadata': {
                'estimated_duration': result.estimated_duration,
                'complexity_score': result.complexity_score,
                'confidence': result.confidence,
                'total_subtasks': len(result.subtasks)
            }
        }
    
    async def create_subtasks_from_decomposition(
        self, 
        parent_task: Task, 
        decomposition_result: Dict[str, Any],
        user_id: str
    ) -> List[Task]:
        """根据分解结果创建子任务"""
        
        subtasks = []
        subtasks_data = decomposition_result['subtasks']
        
        for subtask_data in subtasks_data:
            task_create = TaskCreate(
                title=subtask_data['title'],
                description=subtask_data['description'],
                task_type=subtask_data['task_type'],
                priority=subtask_data['priority'],
                estimated_duration=subtask_data['estimated_duration'],
                metadata={
                    'parent_task_id': str(parent_task.id),
                    'order_index': subtask_data['order_index'],
                    'is_parallel': subtask_data['is_parallel'],
                    'decomposition_confidence': decomposition_result['metadata']['confidence']
                }
            )
            
            subtask = await self._create_task(task_create, user_id, parent_task.id)
            subtasks.append(subtask)
        
        # 创建依赖关系
        await self._create_task_dependencies(subtasks, decomposition_result['dependencies'])
        
        return subtasks
```

---

## 🎛 成本控制和优化

### 成本预算 (月度) - 10万用户规模
```yaml
基础设施成本:
  计算资源:
    - Kubernetes集群: $2,000 (简化配置)
    - 负载均衡器: $300
    - CDN服务: $500
    小计: $2,800

  数据库服务:
    - PostgreSQL (1主2从): $1,200
    - Redis (主从): $400
    小计: $1,600

AI服务成本:
  - 通义千问API: $3,000 (主要模型，100万次调用)
  - Kimi API: $2,000 (备选模型，50万次调用)
  - 豆包API: $1,500 (备选模型，30万次调用)
  - 语音识别服务: $800
  小计: $7,300

运营成本:
  - 监控服务: $200
  - 日志服务: $300
  - 备份存储: $300
  小计: $800

总计: $12,500/月
单用户成本: $0.125/月 (10万用户)

成本优化策略:
- 国产AI模型成本更低(相比GPT节省60%)
- 智能路由减少高成本模型调用
- 结果缓存减少重复计算
- 按需扩容控制资源成本
```

### AI模型配置示例

```python
# app/core/config.py
class Settings(BaseSettings):
    # 通义千问配置
    TONGYI_QIANWEN_API_KEY: Optional[str] = None
    TONGYI_QIANWEN_ENDPOINT: str = "https://dashscope.aliyuncs.com/api/v1/"
    
    # 豆包配置
    DOUBAO_API_KEY: Optional[str] = None
    DOUBAO_ENDPOINT: str = "https://ark.cn-beijing.volces.com/api/v3/"
    
    # Kimi配置  
    KIMI_API_KEY: Optional[str] = None
    KIMI_ENDPOINT: str = "https://api.moonshot.cn/v1"
    
    # OpenAI配置 (备选)
    OPENAI_API_KEY: Optional[str] = None
    
    # AI模型优先级
    AI_MODEL_PRIORITY: List[str] = [
        "tongyi_qianwen",
        "kimi", 
        "doubao",
        "openai_gpt"
    ]
    
    # AI服务配置
    AI_REQUEST_TIMEOUT: int = 30  # 秒
    AI_MAX_RETRIES: int = 3
    AI_CACHE_TTL: int = 3600  # 1小时
    
    class Config:
        env_file = ".env"
```

---

## 📋 实施路线图 (12周)

### 第一阶段：基础架构 (4周)
```yaml
Week 1-2: 环境搭建和基础服务
  - 设置开发环境和CI/CD
  - 部署PostgreSQL主从架构
  - 配置Redis缓存集群
  - 实现用户认证服务
  - 创建基础API框架

Week 3-4: 多AI模型接口
  - 实现AI模型适配器架构
  - 集成通义千问、豆包、Kimi模型
  - 开发模型管理器和降级机制
  - 实现任务分解标准化接口
```

### 第二阶段：核心功能 (4周)
```yaml
Week 5-6: 对话控制中枢
  - 实现SSE连接管理
  - 开发意图识别引擎
  - 实现多模态输入处理
  - 构建对话状态管理

Week 7-8: 任务服务和AI引擎
  - 实现任务CRUD操作
  - 开发智能任务分解功能
  - 集成多种任务类型处理
  - 实现依赖关系管理
```

### 第三阶段：前端和优化 (4周)
```yaml
Week 9-10: Flutter前端开发
  - 实现核心界面和交互
  - 集成SSE实时通信
  - 开发语音输入功能
  - 实现离线缓存机制

Week 11-12: 测试和部署
  - 性能测试和优化
  - 安全测试和加固
  - 生产环境部署
  - 用户体验测试
```

---

## 🎯 关键成功指标

### 技术指标
- **系统可用性**: 99.9%
- **API响应时间**: 平均<200ms，P99<1s  
- **AI推理时间**: <3s (任务分解)
- **并发支持**: 8,000用户同时在线

### 业务指标
- **用户留存率**: 日留存>60%，月留存>30%
- **功能使用率**: 任务分解使用率>70%
- **用户满意度**: AI分解准确率>85%

### 成本指标
- **单用户成本**: <$0.15/月
- **AI成本占比**: <55%
- **基础设施利用率**: >70%

---

## 📚 总结

这个10万用户规模的架构方案具有以下特点：

### ✅ 优势
1. **成本可控**: 月度成本$12,500，单用户成本仅$0.125
2. **架构简化**: 本地语音识别+SSE+HTTP，无WebSocket复杂性
3. **隐私保护**: 语音数据本地处理，不上传服务器
4. **AI多样化**: 支持多个国产AI模型，成本更低
5. **标准化输出**: 统一的任务分解格式和质量保证
6. **渐进开发**: 12周开发周期，适合中小团队

### 🎯 核心亮点
- **对话控制中枢**作为统一入口，提供自然交互体验
- **多AI模型**智能路由和降级，保证服务稳定性
- **标准化任务分解**，支持简单提醒、复杂任务、顺序任务
- **成本优化**，相比国外模型节省60%AI成本
- **通信简化**，SSE+HTTP替代WebSocket，提高可靠性

这个指南提供了从概念到实现的完整路径，可以作为开发团队的详细参考文档。每个阶段都有明确的目标和可交付成果，确保项目能够按计划推进。

建议开发顺序：
1. **先搭建基础架构**和多AI模型接口
2. **重点开发对话控制中枢**，确保用户交互体验
3. **完善任务分解功能**，实现核心业务价值
4. **优化性能和用户体验**，准备生产部署

### 三大引擎实现

#### 1. 对话控制中枢

```python
# app/engines/dialogue_control/intent_recognition.py
import openai
from typing import Dict, List, Optional, Tuple
from pydantic import BaseModel
import json
import re
from ...core.config import settings

class Intent(BaseModel):
    name: str
    confidence: float
    entities: Dict[str, any] = {}

class IntentRecognitionEngine:
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY
        self.supported_intents = [
            "create_task",
            "update_task", 
            "delete_task",
            "query_tasks",
            "schedule_meeting",
            "set_reminder",
            "analyze_efficiency",
            "get_suggestions",
            "general_chat"
        ]

    async def recognize_intent(self, user_message: str, context: Optional[Dict] = None) -> Intent:
        """识别用户意图"""
        
        # 构建提示词
        prompt = self._build_intent_prompt(user_message, context)
        
        try:
            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.1,
                max_tokens=500
            )
            
            result_text = response.choices[0].message.content.strip()
            return self._parse_intent_result(result_text)
            
        except Exception as e:
            print(f"Intent recognition error: {e}")
            return Intent(name="general_chat", confidence=0.5)

    def _build_intent_prompt(self, message: str, context: Optional[Dict] = None) -> str:
        context_info = ""
        if context:
            context_info = f"对话上下文: {json.dumps(context, ensure_ascii=False)}\n"
        
        return f"""
你是一个智能任务管理助手的意图识别模块。请分析用户输入并识别其意图。

支持的意图类型:
- create_task: 创建新任务
- update_task: 更新现有任务
- delete_task: 删除任务
- query_tasks: 查询任务状态或列表
- schedule_meeting: 安排会议
- set_reminder: 设置提醒
- analyze_efficiency: 分析工作效率
- get_suggestions: 获取优化建议
- general_chat: 一般对话

{context_info}

请以JSON格式返回结果:
{{
    "intent": "意图名称",
    "confidence": 置信度(0-1),
    "entities": {{
        "task_title": "任务标题",
        "due_date": "截止时间",
        "priority": "优先级",
        "task_type": "任务类型",
        "action": "具体动作"
    }}
}}

用户输入: {message}
"""

    def _parse_intent_result(self, result_text: str) -> Intent:
        """解析意图识别结果"""
        try:
            # 尝试解析JSON
            result_json = json.loads(result_text)
            
            return Intent(
                name=result_json.get("intent", "general_chat"),
                confidence=result_json.get("confidence", 0.5),
                entities=result_json.get("entities", {})
            )
        except json.JSONDecodeError:
            # 如果无法解析JSON，使用规则方法
            return self._rule_based_intent_recognition(result_text)

    def _rule_based_intent_recognition(self, message: str) -> Intent:
        """基于规则的意图识别备用方案"""
        message_lower = message.lower()
        
        # 任务创建关键词
        create_keywords = ["创建", "添加", "新建", "安排", "要做", "需要做", "计划"]
        if any(keyword in message_lower for keyword in create_keywords):
            return Intent(name="create_task", confidence=0.8)
        
        # 查询关键词
        query_keywords = ["查看", "显示", "列出", "有什么", "今天", "明天", "本周"]
        if any(keyword in message_lower for keyword in query_keywords):
            return Intent(name="query_tasks", confidence=0.8)
        
        # 分析关键词
        analyze_keywords = ["分析", "效率", "统计", "报告", "总结"]
        if any(keyword in message_lower for keyword in analyze_keywords):
            return Intent(name="analyze_efficiency", confidence=0.8)
        
        return Intent(name="general_chat", confidence=0.6)

# app/engines/dialogue_control/dialogue_manager.py
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
import json
import uuid
from datetime import datetime, timedelta
from ...core.database import get_redis
from .intent_recognition import IntentRecognitionEngine, Intent

class DialogueState(BaseModel):
    session_id: str
    user_id: str
    current_intent: Optional[str] = None
    context: Dict[str, Any] = {}
    conversation_history: List[Dict] = []
    last_updated: datetime = datetime.now()

class DialogueManager:
    def __init__(self):
        self.redis = get_redis()
        self.intent_engine = IntentRecognitionEngine()
        self.context_ttl = 3600  # 1小时

    async def process_message(self, user_id: str, message: str, session_id: Optional[str] = None) -> Dict:
        """处理用户消息"""
        
        # 获取或创建对话状态
        if session_id:
            state = await self.get_dialogue_state(session_id)
        else:
            state = await self.create_new_session(user_id)
            session_id = state.session_id

        # 意图识别
        intent = await self.intent_engine.recognize_intent(message, state.context)
        
        # 更新对话状态
        state.current_intent = intent.name
        state.conversation_history.append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat(),
            "intent": intent.dict()
        })
        
        # 基于意图处理消息
        response = await self.handle_intent(state, intent, message)
        
        # 保存响应到历史
        state.conversation_history.append({
            "role": "assistant", 
            "content": response["message"],
            "timestamp": datetime.now().isoformat(),
            "actions": response.get("actions", [])
        })
        
        # 更新上下文
        await self.update_context(state, intent, response)
        
        # 保存状态
        await self.save_dialogue_state(state)
        
        return {
            "session_id": session_id,
            "message": response["message"],
            "actions": response.get("actions", []),
            "intent": intent.dict()
        }

    async def handle_intent(self, state: DialogueState, intent: Intent, message: str) -> Dict:
        """根据意图处理消息"""
        
        handlers = {
            "create_task": self._handle_create_task,
            "update_task": self._handle_update_task,
            "query_tasks": self._handle_query_tasks,
            "schedule_meeting": self._handle_schedule_meeting,
            "analyze_efficiency": self._handle_analyze_efficiency,
            "get_suggestions": self._handle_get_suggestions,
            "general_chat": self._handle_general_chat
        }
        
        handler = handlers.get(intent.name, self._handle_general_chat)
        return await handler(state, intent, message)

    async def _handle_create_task(self, state: DialogueState, intent: Intent, message: str) -> Dict:
        """处理创建任务意图"""
        
        entities = intent.entities
        
        # 检查必需的实体
        if not entities.get("task_title"):
            return {
                "message": "请告诉我您要创建的任务标题是什么？",
                "actions": ["request_task_title"]
            }
        
        # 构建任务数据
        task_data = {
            "title": entities["task_title"],
            "description": entities.get("description"),
            "task_type": entities.get("task_type", "work"),
            "priority": entities.get("priority", 3),
            "due_date": entities.get("due_date")
        }
        
        return {
            "message": f"好的，我将为您创建任务：{task_data['title']}。",
            "actions": [{"type": "create_task", "data": task_data}]
        }

    async def _handle_query_tasks(self, state: DialogueState, intent: Intent, message: str) -> Dict:
        """处理查询任务意图"""
        
        entities = intent.entities
        query_params = {
            "status": entities.get("status"),
            "task_type": entities.get("task_type"),
            "date_range": entities.get("date_range", "today")
        }
        
        return {
            "message": "让我为您查询相关任务...",
            "actions": [{"type": "query_tasks", "data": query_params}]
        }

    async def _handle_general_chat(self, state: DialogueState, intent: Intent, message: str) -> Dict:
        """处理一般对话"""
        
        # 使用GPT生成回复
        response = await self._generate_chat_response(message, state.context)
        
        return {
            "message": response,
            "actions": []
        }

    async def _generate_chat_response(self, message: str, context: Dict) -> str:
        """生成对话回复"""
        # 这里可以调用GPT API生成更智能的回复
        return "我理解了您的意思，有什么我可以帮助您的吗？"
```

#### 2. 任务拆解引擎

```python
# app/engines/task_decomposition/decomposition_engine.py
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
import openai
import json
from datetime import datetime, timedelta
from ...models.task import Task
from ...schemas.task import TaskCreate
from ...core.config import settings

class SubtaskData(BaseModel):
    title: str
    description: Optional[str]
    task_type: str
    priority: int
    difficulty: int
    estimated_duration: Optional[int]  # minutes
    dependencies: List[str] = []  # 依赖的子任务标题
    order_index: int

class TaskDecompositionEngine:
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY

    async def decompose_task(
        self, 
        task: Task, 
        decomposition_level: int = 2,
        auto_schedule: bool = True
    ) -> List[TaskCreate]:
        """将任务智能拆解为子任务"""
        
        # 使用AI进行任务拆解
        subtasks_data = await self._ai_decompose_task(task, decomposition_level)
        
        # 计算依赖关系和优先级
        subtasks_data = self._calculate_dependencies_and_priorities(subtasks_data)
        
        # 如果启用自动安排，计算时间安排
        if auto_schedule:
            subtasks_data = self._schedule_subtasks(subtasks_data, task.due_date)
        
        # 转换为TaskCreate对象
        task_creates = []
        for i, subtask_data in enumerate(subtasks_data):
            task_create = TaskCreate(
                title=subtask_data.title,
                description=subtask_data.description,
                task_type=subtask_data.task_type,
                priority=subtask_data.priority,
                difficulty=subtask_data.difficulty,
                estimated_duration=subtask_data.estimated_duration,
                due_date=getattr(subtask_data, 'due_date', None),
                metadata={
                    "decomposition_level": 1,
                    "order_index": subtask_data.order_index,
                    "dependencies": subtask_data.dependencies
                }
            )
            task_creates.append(task_create)
        
        return task_creates

    async def _ai_decompose_task(self, task: Task, decomposition_level: int) -> List[SubtaskData]:
        """使用AI拆解任务"""
        
        prompt = self._build_decomposition_prompt(task, decomposition_level)
        
        try:
            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": f"请拆解任务：{task.title}"}
                ],
                temperature=0.3,
                max_tokens=2000
            )
            
            result_text = response.choices[0].message.content.strip()
            return self._parse_decomposition_result(result_text)
            
        except Exception as e:
            print(f"AI decomposition error: {e}")
            return self._fallback_decomposition(task)

    def _build_decomposition_prompt(self, task: Task, decomposition_level: int) -> str:
        return f"""
你是一个专业的任务管理专家。请将用户的任务智能拆解为可执行的子任务。

任务信息:
- 标题: {task.title}
- 描述: {task.description or "无"}
- 类型: {task.task_type}
- 优先级: {task.priority} (1-5, 1最高)
- 难度: {task.difficulty} (1-5, 1最简单)
- 预估时长: {task.estimated_duration or "未设置"}分钟

拆解要求:
1. 拆解层级: {decomposition_level}
2. 每个子任务应该是原子化的、可执行的
3. 子任务之间应该有清晰的逻辑顺序
4. 估算每个子任务的时间和难度
5. 识别子任务间的依赖关系

请以JSON格式返回结果:
[
  {{
    "title": "子任务标题",
    "description": "子任务描述",
    "task_type": "任务类型",
    "priority": 优先级(1-5),
    "difficulty": 难度(1-5),
    "estimated_duration": 估算时长(分钟),
    "dependencies": ["依赖的子任务标题"],
    "order_index": 执行顺序(从0开始)
  }}
]

请确保返回的是有效的JSON格式。
"""

    def _parse_decomposition_result(self, result_text: str) -> List[SubtaskData]:
        """解析AI拆解结果"""
        try:
            result_list = json.loads(result_text)
            subtasks = []
            
            for i, item in enumerate(result_list):
                subtask = SubtaskData(
                    title=item.get("title", f"子任务 {i+1}"),
                    description=item.get("description"),
                    task_type=item.get("task_type", "work"),
                    priority=item.get("priority", 3),
                    difficulty=item.get("difficulty", 3),
                    estimated_duration=item.get("estimated_duration"),
                    dependencies=item.get("dependencies", []),
                    order_index=item.get("order_index", i)
                )
                subtasks.append(subtask)
            
            return subtasks
            
        except json.JSONDecodeError:
            print("Failed to parse AI decomposition result")
            return []

    def _calculate_dependencies_and_priorities(self, subtasks: List[SubtaskData]) -> List[SubtaskData]:
        """计算依赖关系和优先级"""
        # 实现依赖关系分析逻辑
        # 这里可以使用图算法来分析和优化依赖关系
        
        return subtasks

    def _schedule_subtasks(self, subtasks: List[SubtaskData], parent_due_date: Optional[datetime]) -> List[SubtaskData]:
        """自动安排子任务的时间"""
        if not parent_due_date:
            return subtasks
        
        # 计算总的估算时间
        total_duration = sum(st.estimated_duration or 60 for st in subtasks)
        
        # 从截止日期倒推，安排各个子任务的时间
        current_date = parent_due_date
        
        for subtask in reversed(subtasks):  # 从后往前安排
            duration = subtask.estimated_duration or 60
            start_time = current_date - timedelta(minutes=duration)
            
            # 添加时间信息到metadata
            subtask.due_date = current_date
            subtask.start_time = start_time
            
            current_date = start_time
        
        return subtasks

    def _fallback_decomposition(self, task: Task) -> List[SubtaskData]:
        """AI拆解失败时的备用方案"""
        return [
            SubtaskData(
                title=f"拆解 - {task.title}",
                description="请手动细化此任务",
                task_type=task.task_type,
                priority=task.priority,
                difficulty=task.difficulty,
                estimated_duration=task.estimated_duration,
                order_index=0
            )
        ]
```

#### 3. 熵减引擎

```python
# app/engines/entropy_reduction/pattern_analyzer.py
from typing import List, Dict, Optional, Any, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import json
from sqlalchemy import text
from ...core.database import get_db
from ...models.analytics import EfficiencyMetric, WorkSession
from ...models.user import UserBehaviorMetric

class PatternAnalyzer:
    def __init__(self):
        self.db = get_db()

    async def analyze_user_patterns(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """分析用户的行为模式"""
        
        # 获取用户数据
        user_data = await self._fetch_user_data(user_id, days)
        
        if not user_data:
            return {"error": "Insufficient data for analysis"}
        
        patterns = {}
        
        # 时间模式分析
        patterns["time_patterns"] = self._analyze_time_patterns(user_data)
        
        # 效率模式分析
        patterns["efficiency_patterns"] = self._analyze_efficiency_patterns(user_data)
        
        # 任务类型模式分析
        patterns["task_type_patterns"] = self._analyze_task_type_patterns(user_data)
        
        # 专注力模式分析
        patterns["focus_patterns"] = self._analyze_focus_patterns(user_data)
        
        # 习惯模式识别
        patterns["habit_patterns"] = self._identify_habit_patterns(user_data)
        
        return patterns

    async def _fetch_user_data(self, user_id: str, days: int) -> Optional[pd.DataFrame]:
        """从PostgreSQL获取用户行为数据"""
        
        # 计算查询时间范围
        start_date = datetime.now() - timedelta(days=days)
        
        try:
            async with self.db as session:
                # 查询用户行为数据
                query = """
                    SELECT 
                        recorded_at as time,
                        user_id,
                        activity_type,
                        duration,
                        efficiency_score,
                        focus_level,
                        metadata
                    FROM user_behavior_metrics 
                    WHERE user_id = :user_id 
                      AND recorded_at >= :start_date
                    ORDER BY recorded_at DESC
                """
                
                result = await session.execute(text(query), {
                    'user_id': user_id,
                    'start_date': start_date
                })
                
                data_list = []
                for row in result:
                    data_list.append({
                        'time': row.time,
                        'user_id': str(row.user_id),
                        'activity_type': row.activity_type,
                        'duration': row.duration,
                        'efficiency_score': row.efficiency_score,
                        'focus_level': row.focus_level,
                        'metadata': row.metadata or {}
                    })
                
                if not data_list:
                    return None
                    
                df = pd.DataFrame(data_list)
                df['time'] = pd.to_datetime(df['time'])
                df.set_index('time', inplace=True)
            
            return df
            
        except Exception as e:
            print(f"Error fetching user data: {e}")
            return None

    def _analyze_time_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析时间模式"""
        
        # 添加时间特征
        data['hour'] = data.index.hour
        data['day_of_week'] = data.index.dayofweek
        data['is_weekend'] = data['day_of_week'].isin([5, 6])
        
        patterns = {}
        
        # 最佳工作时段分析
        hourly_efficiency = data.groupby('hour')['efficiency_score'].mean()
        patterns['peak_hours'] = hourly_efficiency.nlargest(3).index.tolist()
        patterns['low_hours'] = hourly_efficiency.nsmallest(3).index.tolist()
        
        # 工作日 vs 周末分析
        workday_efficiency = data[~data['is_weekend']]['efficiency_score'].mean()
        weekend_efficiency = data[data['is_weekend']]['efficiency_score'].mean()
        
        patterns['workday_vs_weekend'] = {
            'workday_avg_efficiency': float(workday_efficiency),
            'weekend_avg_efficiency': float(weekend_efficiency),
            'weekend_preference': weekend_efficiency > workday_efficiency
        }
        
        # 每日模式聚类
        daily_patterns = self._cluster_daily_patterns(data)
        patterns['daily_clusters'] = daily_patterns
        
        return patterns

    def _analyze_efficiency_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析效率模式"""
        
        patterns = {}
        
        # 效率趋势分析
        daily_efficiency = data.groupby(data.index.date)['efficiency_score'].mean()
        
        # 计算趋势
        x = np.arange(len(daily_efficiency))
        y = daily_efficiency.values
        trend_coeff = np.polyfit(x, y, 1)[0]
        
        patterns['efficiency_trend'] = {
            'trend_direction': 'improving' if trend_coeff > 0 else 'declining',
            'trend_strength': abs(float(trend_coeff)),
            'average_efficiency': float(daily_efficiency.mean()),
            'efficiency_variance': float(daily_efficiency.var())
        }
        
        # 效率影响因素分析
        correlation_factors = self._analyze_efficiency_correlations(data)
        patterns['efficiency_factors'] = correlation_factors
        
        return patterns

    def _analyze_task_type_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析任务类型模式"""
        
        patterns = {}
        
        # 不同任务类型的效率
        task_efficiency = data.groupby('task_type')['efficiency_score'].agg(['mean', 'std', 'count'])
        
        patterns['task_type_efficiency'] = {}
        for task_type in task_efficiency.index:
            patterns['task_type_efficiency'][task_type] = {
                'avg_efficiency': float(task_efficiency.loc[task_type, 'mean']),
                'efficiency_std': float(task_efficiency.loc[task_type, 'std']),
                'sample_count': int(task_efficiency.loc[task_type, 'count'])
            }
        
        # 最适合的任务类型时间段
        task_time_efficiency = data.groupby(['task_type', data.index.hour])['efficiency_score'].mean().unstack(fill_value=0)
        
        patterns['best_time_for_tasks'] = {}
        for task_type in task_time_efficiency.index:
            best_hour = task_time_efficiency.loc[task_type].idxmax()
            patterns['best_time_for_tasks'][task_type] = int(best_hour)
        
        return patterns

    def _analyze_focus_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析专注力模式"""
        
        patterns = {}
        
        # 专注力周期分析
        focus_data = data['focus_level'].dropna()
        
        if len(focus_data) > 0:
            # 寻找专注力峰值和低谷
            patterns['focus_statistics'] = {
                'avg_focus': float(focus_data.mean()),
                'max_focus': float(focus_data.max()),
                'min_focus': float(focus_data.min()),
                'focus_variance': float(focus_data.var())
            }
            
            # 专注力持续时间分析
            focus_sessions = self._identify_focus_sessions(data)
            patterns['focus_sessions'] = focus_sessions
        
        return patterns

    def _identify_habit_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """识别习惯模式"""
        
        patterns = {}
        
        # 工作习惯识别
        patterns['work_habits'] = self._identify_work_habits(data)
        
        # 拖延模式识别
        patterns['procrastination_patterns'] = self._identify_procrastination_patterns(data)
        
        # 效率提升习惯
        patterns['productivity_habits'] = self._identify_productivity_habits(data)
        
        return patterns

    def _cluster_daily_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """聚类每日模式"""
        
        # 按日期聚合特征
        daily_features = data.groupby(data.index.date).agg({
            'efficiency_score': ['mean', 'std'],
            'focus_level': ['mean', 'max'],
            'duration': 'sum',
            'hour': ['min', 'max']  # 工作时间范围
        }).fillna(0)
        
        # 展平列名
        daily_features.columns = ['_'.join(col).strip() for col in daily_features.columns]
        
        if len(daily_features) < 3:
            return {"error": "Insufficient data for clustering"}
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(daily_features)
        
        # K-means聚类
        n_clusters = min(4, len(daily_features) // 3)  # 动态确定聚类数
        if n_clusters >= 2:
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(features_scaled)
            
            # 分析每个聚类的特征
            cluster_analysis = {}
            for i in range(n_clusters):
                cluster_data = daily_features[clusters == i]
                cluster_analysis[f'cluster_{i}'] = {
                    'size': len(cluster_data),
                    'avg_efficiency': float(cluster_data['efficiency_score_mean'].mean()),
                    'avg_focus': float(cluster_data['focus_level_mean'].mean()),
                    'description': self._describe_cluster(cluster_data)
                }
            
            return {
                'n_clusters': n_clusters,
                'cluster_analysis': cluster_analysis
            }
        
        return {"error": "Unable to perform clustering"}

    def _describe_cluster(self, cluster_data: pd.DataFrame) -> str:
        """描述聚类特征"""
        avg_efficiency = cluster_data['efficiency_score_mean'].mean()
        avg_focus = cluster_data['focus_level_mean'].mean()
        avg_duration = cluster_data['duration_sum'].mean()
        
        if avg_efficiency > 0.8:
            efficiency_desc = "高效"
        elif avg_efficiency > 0.6:
            efficiency_desc = "中等效率"
        else:
            efficiency_desc = "低效率"
        
        if avg_focus > 0.8:
            focus_desc = "高度专注"
        elif avg_focus > 0.6:
            focus_desc = "适度专注"
        else:
            focus_desc = "容易分心"
        
        return f"{efficiency_desc}，{focus_desc}的工作模式"

# app/engines/entropy_reduction/optimization_engine.py
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
import numpy as np
from ...models.analytics import OptimizationSuggestion
from .pattern_analyzer import PatternAnalyzer

class OptimizationEngine:
    def __init__(self):
        self.pattern_analyzer = PatternAnalyzer()

    async def generate_optimization_suggestions(self, user_id: str) -> List[Dict[str, Any]]:
        """生成个性化优化建议"""
        
        # 分析用户模式
        patterns = await self.pattern_analyzer.analyze_user_patterns(user_id)
        
        if "error" in patterns:
            return []
        
        suggestions = []
        
        # 时间管理优化建议
        time_suggestions = self._generate_time_optimization_suggestions(patterns.get("time_patterns", {}))
        suggestions.extend(time_suggestions)
        
        # 效率提升建议
        efficiency_suggestions = self._generate_efficiency_suggestions(patterns.get("efficiency_patterns", {}))
        suggestions.extend(efficiency_suggestions)
        
        # 专注力改善建议
        focus_suggestions = self._generate_focus_suggestions(patterns.get("focus_patterns", {}))
        suggestions.extend(focus_suggestions)
        
        # 任务类型优化建议
        task_suggestions = self._generate_task_optimization_suggestions(patterns.get("task_type_patterns", {}))
        suggestions.extend(task_suggestions)
        
        # 习惯养成建议
        habit_suggestions = self._generate_habit_suggestions(patterns.get("habit_patterns", {}))
        suggestions.extend(habit_suggestions)
        
        return suggestions

    def _generate_time_optimization_suggestions(self, time_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成时间管理优化建议"""
        suggestions = []
        
        if "peak_hours" in time_patterns:
            peak_hours = time_patterns["peak_hours"]
            if peak_hours:
                suggestion = {
                    "type": "time_management",
                    "title": "优化高效时段安排",
                    "description": f"您在{peak_hours}点效率最高，建议将重要任务安排在这些时间段。",
                    "priority": 4,
                    "category": "时间规划",
                    "actionable_steps": [
                        f"将重要任务安排在{peak_hours[0]}-{peak_hours[-1]}点之间",
                        "在高效时段避免安排会议或琐碎事务",
                        "利用高效时段处理需要深度思考的工作"
                    ]
                }
                suggestions.append(suggestion)
        
        if "low_hours" in time_patterns:
            low_hours = time_patterns["low_hours"]
            if low_hours:
                suggestion = {
                    "type": "time_management",
                    "title": "低效时段优化",
                    "description": f"您在{low_hours}点效率较低，建议安排轻松的任务或休息。",
                    "priority": 3,
                    "category": "时间规划",
                    "actionable_steps": [
                        f"在{low_hours[0]}-{low_hours[-1]}点安排休息或轻松任务",
                        "利用这个时间段进行学习充电",
                        "考虑在低效时段进行运动或放松活动"
                    ]
                }
                suggestions.append(suggestion)
        
        return suggestions

    def _generate_efficiency_suggestions(self, efficiency_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成效率提升建议"""
        suggestions = []
        
        efficiency_trend = efficiency_patterns.get("efficiency_trend", {})
        
        if efficiency_trend.get("trend_direction") == "declining":
            suggestion = {
                "type": "efficiency_improvement",
                "title": "效率下降趋势警报",
                "description": "检测到您的工作效率有下降趋势，建议采取措施改善。",
                "priority": 5,
                "category": "效率优化",
                "actionable_steps": [
                    "分析最近工作中的干扰因素",
                    "适当增加休息时间",
                    "重新评估工作方法和工具",
                    "考虑调整工作环境"
                ]
            }
            suggestions.append(suggestion)
        
        avg_efficiency = efficiency_trend.get("average_efficiency", 0)
        if avg_efficiency < 0.6:
            suggestion = {
                "type": "efficiency_improvement", 
                "title": "整体效率待提升",
                "description": f"您的平均效率为{avg_efficiency:.2f}，还有较大提升空间。",
                "priority": 4,
                "category": "效率优化",
                "actionable_steps": [
                    "尝试番茄工作法或其他时间管理技巧",
                    "减少任务切换频率",
                    "优化工作环境减少干扰",
                    "设定明确的工作目标"
                ]
            }
            suggestions.append(suggestion)
        
        return suggestions

    def _generate_focus_suggestions(self, focus_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成专注力改善建议"""
        suggestions = []
        
        focus_stats = focus_patterns.get("focus_statistics", {})
        
        avg_focus = focus_stats.get("avg_focus", 0)
        if avg_focus < 0.7:
            suggestion = {
                "type": "focus_improvement",
                "title": "专注力有待提升",
                "description": f"您的平均专注水平为{avg_focus:.2f}，建议采用以下方法提升专注力。",
                "priority": 4,
                "category": "专注力训练",
                "actionable_steps": [
                    "尝试冥想或正念练习",
                    "使用专注力训练应用",
                    "减少工作环境中的干扰源",
                    "制定明确的单任务工作计划",
                    "定期进行短暂休息"
                ]
            }
            suggestions.append(suggestion)
        
        focus_variance = focus_stats.get("focus_variance", 0)
        if focus_variance > 0.3:  # 专注力波动较大
            suggestion = {
                "type": "focus_improvement",
                "title": "专注力波动较大",
                "description": "您的专注力波动较大，建议建立稳定的工作节奏。",
                "priority": 3,
                "category": "专注力稳定",
                "actionable_steps": [
                    "建立固定的工作时间表",
                    "创建标准化的工作开始仪式",
                    "保持规律的睡眠和饮食",
                    "使用一致的工作环境设置"
                ]
            }
            suggestions.append(suggestion)
        
        return suggestions

    def _generate_task_optimization_suggestions(self, task_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成任务类型优化建议"""
        suggestions = []
        
        task_efficiency = task_patterns.get("task_type_efficiency", {})
        best_times = task_patterns.get("best_time_for_tasks", {})
        
        # 找出效率最低的任务类型
        if task_efficiency:
            min_efficiency_task = min(task_efficiency.items(), key=lambda x: x[1]['avg_efficiency'])
            if min_efficiency_task[1]['avg_efficiency'] < 0.6:
                task_type, efficiency_data = min_efficiency_task
                suggestion = {
                    "type": "task_optimization",
                    "title": f"改善{task_type}类型任务效率",
                    "description": f"您在{task_type}类型任务上效率较低，建议进行优化。",
                    "priority": 3,
                    "category": "任务优化",
                    "actionable_steps": [
                        f"分析{task_type}任务中的困难点",
                        "考虑将复杂任务拆分为更小的部分",
                        "寻找更适合的工具或方法",
                        f"在{best_times.get(task_type, '上午')}时段处理{task_type}任务"
                    ]
                }
                suggestions.append(suggestion)
        
        # 根据最佳时间安排给出建议
        if best_times:
            suggestion = {
                "type": "task_scheduling",
                "title": "任务时间安排优化",
                "description": "根据您的历史数据，为不同类型任务推荐最佳时间安排。",
                "priority": 3,
                "category": "时间安排",
                "actionable_steps": [
                    f"建议时间安排：{', '.join([f'{task}在{time}点' for task, time in best_times.items()])}",
                    "尝试按照推荐时间安排任务一周",
                    "观察效率是否有所提升"
                ]
            }
            suggestions.append(suggestion)
        
        return suggestions

    def _generate_habit_suggestions(self, habit_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成习惯养成建议"""
        suggestions = []
        
        # 这里可以基于识别出的习惯模式生成相应建议
        work_habits = habit_patterns.get("work_habits", {})
        procrastination = habit_patterns.get("procrastination_patterns", {})
        
        if procrastination.get("high_procrastination", False):
            suggestion = {
                "type": "habit_formation",
                "title": "减少拖延行为",
                "description": "检测到您有拖延的倾向，建议采用以下策略改善。",
                "priority": 4,
                "category": "习惯改善",
                "actionable_steps": [
                    "使用"两分钟规则"：如果任务少于2分钟就立即完成",
                    "将大任务分解为小步骤",
                    "设置时间盒，专注工作25分钟",
                    "建立奖励机制，完成任务后给自己小奖励"
                ]
            }
            suggestions.append(suggestion)
        
        return suggestions
```

---

## 开发阶段规划

### Phase 1: 基础架构搭建 (2-3周)

#### 第1周: 环境配置和数据库设计
- 设置开发环境 (Python, Flutter, Docker)
- 配置PostgreSQL, Redis
- 创建数据库表结构和迁移脚本
- 设置基础的CI/CD pipeline

#### 第2周: 后端API基础框架
- 实现FastAPI应用基础结构
- 创建用户认证系统 (JWT)
- 实现基础的CRUD操作
- 设置API文档 (Swagger)

#### 第3周: Flutter应用基础
- 创建Flutter项目结构
- 实现基础的UI组件
- 配置状态管理 (Riverpod)
- 实现用户注册/登录界面

### Phase 2: 对话控制中枢 (3-4周)

#### 第4周: 意图识别系统
- 集成OpenAI GPT API
- 实现意图识别引擎
- 创建意图分类器
- 实现实体抽取功能

#### 第5周: 对话管理系统
- 实现对话状态管理
- 创建上下文记忆系统
- 实现多轮对话支持
- 集成Redis缓存对话状态

#### 第6周: 多模态输入处理
- 集成本地语音识别 (iOS/Android)
- 实现图像识别功能
- 创建多模态数据融合
- 实现Flutter语音输入组件

#### 第7周: 输出生成和优化
- 实现自然语言响应生成
- 创建个性化输出调整
- 优化对话体验
- 进行测试和调优

### Phase 3: 任务拆解引擎 (3-4周)

#### 第8周: 任务解析系统
- 实现任务描述理解
- 创建复杂度评估模型
- 实现时间预估算法
- 设计资源需求分析

#### 第9周: 智能拆解算法
- 实现层次化任务分解
- 创建依赖关系分析
- 实现里程碑提取
- 设计可行性验证

#### 第10周: 优先级和调度系统
- 实现多维度权重计算
- 创建动态优先级调整
- 实现冲突解决机制
- 设计自动调度算法

#### 第11周: 执行计划生成
- 实现时间窗口分配
- 创建提醒策略系统
- 实现进度跟踪
- 设计自适应调整

### Phase 4: 熵减引擎 (4-5周)

#### 第12周: 数据采集系统
- 实现行为数据记录
- 创建环境因素监测
- 集成生理指标采集
- 设计主观状态评估

#### 第13周: 模式分析引擎
- 实现时间序列分析
- 创建聚类分析算法
- 实现关联规则挖掘
- 设计异常检测系统

#### 第14周: 效率评估系统
- 实现多维效率指标
- 创建基准对比分析
- 实现趋势预测模型
- 设计性能监控

#### 第15周: 优化策略生成
- 实现个性化推荐算法
- 创建A/B测试框架
- 实现渐进式改进
- 设计反馈循环机制

#### 第16周: 自适应学习系统
- 实现强化学习模型
- 创建用户反馈整合
- 实现策略效果评估
- 设计模型持续优化

### Phase 5: 前端集成和完善 (2-3周)

#### 第17周: 主界面开发
- 实现主要界面设计
- 创建任务管理界面
- 实现数据可视化
- 集成聊天界面

#### 第18周: 高级功能集成
- 实现语音交互功能
- 创建智能提醒系统
- 实现离线功能支持
- 优化用户体验

#### 第19周: 测试和部署
- 进行端到端测试
- 性能优化和调试
- 准备生产环境部署
- 编写用户文档

---

## Docker配置

### docker-compose.yml

```yaml
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: betterp_postgres
    environment:
      POSTGRES_DB: betterp
      POSTGRES_USER: betterp
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - betterp_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: betterp_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - betterp_network


  # Python后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: betterp_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*******************************************/betterp
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - betterp_network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: betterp_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - betterp_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  betterp_network:
    driver: bridge
```

### 后端Dockerfile

```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

### requirements.txt

```txt
# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
redis==5.0.1

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-bcrypt==4.1.2

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# AI和机器学习
openai==1.3.7
scikit-learn==1.3.2
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4

# 自然语言处理
spacy==3.7.2
transformers==4.36.2
torch==2.1.1

# 语音处理
SpeechRecognition==3.10.0
pyttsx3==2.90

# 图像处理
Pillow==10.1.0
opencv-python==********

# 工具库
python-dotenv==1.0.0
requests==2.31.0
aiofiles==23.2.1
python-slugify==8.0.1
email-validator==2.1.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# 监控和日志
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0
prometheus-client==0.19.0

# CORS
python-cors==1.0.0
```

---

## 部署和监控

### 生产环境配置

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: betterp_prod
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    networks:
      - betterp_prod_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_prod_data:/data
    networks:
      - betterp_prod_network
    restart: always

  backend:
    image: betterp/backend:latest
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/betterp_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis
    networks:
      - betterp_prod_network
    restart: always
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - static_files:/var/www/static
    depends_on:
      - backend
    networks:
      - betterp_prod_network
    restart: always

volumes:
  postgres_prod_data:
  redis_prod_data:
  static_files:

networks:
  betterp_prod_network:
    driver: overlay
    attachable: true
```

### 监控配置

```yaml
# monitoring/docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - monitoring

  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring:
    driver: bridge
```

---

## 总结

这个全面的开发指南涵盖了BetterP智能生活管理应用的所有关键方面：

1. **技术架构**: 基于三大核心引擎的微服务架构
2. **数据库设计**: 完整的PostgreSQL表结构设计
3. **前端开发**: Flutter跨平台应用开发指南
4. **后端实现**: Python FastAPI服务端实现
5. **AI引擎**: 三大核心引擎的详细实现方案
6. **开发流程**: 分阶段的开发计划和里程碑
7. **部署运维**: Docker容器化和生产环境配置

该指南提供了从概念到实现的完整路径，可以作为开发团队的详细参考文档。每个阶段都有明确的目标和可交付成果，确保项目能够按计划推进。

接下来您可以根据这个指南开始实际的开发工作，建议先从Phase 1的基础架构搭建开始。

---

## 📋 任务清单

详细的项目任务清单请参考：**[BetterP-任务清单.md](./BetterP-任务清单.md)**

包含：
- ✅ 详细任务分解 (64个具体任务)
- ✅ 完成状态跟踪
- ✅ 里程碑检查点
- ✅ 依赖关系管理
- ✅ 团队分工建议
- ✅ 每周进度报告模板