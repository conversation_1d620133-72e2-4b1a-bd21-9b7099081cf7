<svg width="1800" height="1350" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498DB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980B9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dialogGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E74C3C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C0392B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="taskGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#27AE60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="entropyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9B59B6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8E44AD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F39C12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E67E22;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-opacity="0.3"/>
    </filter>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#34495e" />
    </marker>
    <marker id="feedbackArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#E67E22" />
    </marker>
  </defs>
  
  <g transform="scale(1.5)">
  <!-- Background -->
  <rect width="1200" height="900" fill="#f8f9fa"/>
  
  <!-- Title -->
  <text x="600" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">三引擎数据流交互图</text>
  
  <!-- User Input/Output -->
  <g transform="translate(500, 80)">
    <ellipse cx="100" cy="40" rx="80" ry="30" fill="url(#userGradient)" stroke="#2980B9" stroke-width="2" filter="url(#shadow)"/>
    <text x="100" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">用户交互界面</text>
    <text x="100" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">多模态输入输出</text>
  </g>
  
  <!-- Dialog Control Engine -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="280" height="160" rx="15" fill="url(#dialogGradient)" stroke="#C0392B" stroke-width="3" filter="url(#shadow)"/>
    <text x="140" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">对话控制中枢</text>
    
    <!-- Internal components -->
    <rect x="20" y="45" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="80" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">意图理解引擎</text>
    
    <rect x="160" y="45" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="210" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">对话管理器</text>
    
    <rect x="20" y="85" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="80" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">上下文记忆</text>
    
    <rect x="160" y="85" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="210" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">输出生成器</text>
    
    <rect x="70" y="125" width="140" height="25" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
    <text x="140" y="142" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">个性化配置模块</text>
  </g>
  
  <!-- Task Decomposition Engine -->
  <g transform="translate(460, 180)">
    <rect x="0" y="0" width="280" height="160" rx="15" fill="url(#taskGradient)" stroke="#229954" stroke-width="3" filter="url(#shadow)"/>
    <text x="140" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">动态任务拆解引擎</text>
    
    <!-- Internal components -->
    <rect x="20" y="45" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="80" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">任务解析器</text>
    
    <rect x="160" y="45" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="210" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">拆解算法</text>
    
    <rect x="20" y="85" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="80" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">优先级引擎</text>
    
    <rect x="160" y="85" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="210" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">计划生成器</text>
    
    <rect x="70" y="125" width="140" height="25" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
    <text x="140" y="142" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">依赖关系分析</text>
  </g>
  
  <!-- Entropy Reduction Engine -->
  <g transform="translate(820, 180)">
    <rect x="0" y="0" width="280" height="160" rx="15" fill="url(#entropyGradient)" stroke="#8E44AD" stroke-width="3" filter="url(#shadow)"/>
    <text x="140" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">熵减引擎</text>
    
    <!-- Internal components -->
    <rect x="20" y="45" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="80" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">数据采集器</text>
    
    <rect x="160" y="45" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="210" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">模式分析</text>
    
    <rect x="20" y="85" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="80" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">效率评估</text>
    
    <rect x="160" y="85" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="210" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">策略生成</text>
    
    <rect x="70" y="125" width="140" height="25" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
    <text x="140" y="142" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">自适应学习系统</text>
  </g>
  
  <!-- Data Storage Layer -->
  <g transform="translate(300, 400)">
    <rect x="0" y="0" width="600" height="100" rx="10" fill="url(#dataGradient)" stroke="#E67E22" stroke-width="2" filter="url(#shadow)"/>
    <text x="300" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">统一数据存储层</text>
    
    <!-- Storage components -->
    <rect x="20" y="40" width="100" height="25" rx="3" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="70" y="57" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">用户数据</text>
    
    <rect x="140" y="40" width="100" height="25" rx="3" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="190" y="57" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">对话历史</text>
    
    <rect x="260" y="40" width="100" height="25" rx="3" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="310" y="57" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">任务数据</text>
    
    <rect x="380" y="40" width="100" height="25" rx="3" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="430" y="57" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">行为数据</text>
    
    <rect x="500" y="40" width="80" height="25" rx="3" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="540" y="57" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">模型数据</text>
    
    <rect x="200" y="70" width="200" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
    <text x="300" y="83" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">知识图谱 &amp; 个性化配置</text>
  </g>
  
  <!-- Data Flow Arrows -->
  
  <!-- User to Dialog Control -->
  <path d="M 580 130 Q 450 150 300 200" stroke="#3498DB" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="440" y="165" font-family="Arial, sans-serif" font-size="10" fill="#2980B9">用户输入</text>
  
  <!-- Dialog Control to User -->
  <path d="M 260 200 Q 400 150 560 130" stroke="#E74C3C" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="410" y="140" font-family="Arial, sans-serif" font-size="10" fill="#C0392B">智能响应</text>
  
  <!-- Dialog to Task -->
  <path d="M 380 260 L 460 260" stroke="#34495e" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="400" y="250" font-family="Arial, sans-serif" font-size="10" fill="#34495e">意图与需求</text>
  
  <!-- Task to Dialog -->
  <path d="M 460 280 L 380 280" stroke="#27AE60" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="400" y="295" font-family="Arial, sans-serif" font-size="10" fill="#229954">执行计划</text>
  
  <!-- Task to Entropy -->
  <path d="M 740 260 L 820 260" stroke="#34495e" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="760" y="250" font-family="Arial, sans-serif" font-size="10" fill="#34495e">任务执行数据</text>
  
  <!-- Entropy to Task -->
  <path d="M 820 280 L 740 280" stroke="#9B59B6" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="760" y="295" font-family="Arial, sans-serif" font-size="10" fill="#8E44AD">优化建议</text>
  
  <!-- Entropy to Dialog (feedback loop) -->
  <path d="M 960 340 Q 960 380 200 380 Q 200 320 240 320" stroke="#E67E22" stroke-width="2" fill="none" stroke-dasharray="8,4" marker-end="url(#feedbackArrow)"/>
  <text x="580" y="395" font-family="Arial, sans-serif" font-size="10" fill="#E67E22">效率反馈与个性化优化</text>
  
  <!-- All engines to Data Storage -->
  <path d="M 240 340 L 350 400" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 600 340 L 550 400" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 960 340 L 750 400" stroke="#34495e" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Data Storage to engines (read operations) -->
  <path d="M 350 400 L 240 340" stroke="#F39C12" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  <path d="M 550 400 L 600 340" stroke="#F39C12" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  <path d="M 750 400 L 960 340" stroke="#F39C12" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- Data Flow Descriptions -->
  <g transform="translate(50, 550)">
    <rect x="0" y="0" width="1100" height="140" rx="8" fill="rgba(255,255,255,0.95)" stroke="#bdc3c7" stroke-width="1"/>
    <text x="550" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">数据流交互详解</text>
    
    <!-- Stage descriptions -->
    <g transform="translate(20, 40)">
      <circle cx="10" cy="10" r="8" fill="#3498DB"/>
      <text x="12" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">1</text>
      <text x="30" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">输入阶段</text>
      <text x="30" y="30" font-family="Arial, sans-serif" font-size="10" fill="#34495e">用户通过多模态接口输入需求，对话控制中枢进行意图理解和上下文分析</text>
    </g>
    
    <g transform="translate(280, 40)">
      <circle cx="10" cy="10" r="8" fill="#27AE60"/>
      <text x="12" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">2</text>
      <text x="30" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">规划阶段</text>
      <text x="30" y="30" font-family="Arial, sans-serif" font-size="10" fill="#34495e">任务拆解引擎接收需求，进行智能分解、优先级计算和执行计划生成</text>
    </g>
    
    <g transform="translate(540, 40)">
      <circle cx="10" cy="10" r="8" fill="#9B59B6"/>
      <text x="12" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">3</text>
      <text x="30" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">监控阶段</text>
      <text x="30" y="30" font-family="Arial, sans-serif" font-size="10" fill="#34495e">熵减引擎实时监控执行过程，收集效率数据并进行模式分析</text>
    </g>
    
    <g transform="translate(800, 40)">
      <circle cx="10" cy="10" r="8" fill="#E74C3C"/>
      <text x="12" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">4</text>
      <text x="30" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">优化阶段</text>
      <text x="30" y="30" font-family="Arial, sans-serif" font-size="10" fill="#34495e">基于分析结果生成优化策略，通过反馈循环持续改进系统性能</text>
    </g>
    
    <!-- Data types -->
    <g transform="translate(20, 80)">
      <text x="0" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">核心数据流类型：</text>
      <text x="0" y="35" font-family="Arial, sans-serif" font-size="10" fill="#34495e">• 用户交互数据：语音、文本、图像、手势等多模态输入</text>
      <text x="0" y="48" font-family="Arial, sans-serif" font-size="10" fill="#34495e">• 任务执行数据：任务状态、完成时间、质量评估、资源使用情况</text>
      <text x="0" y="61" font-family="Arial, sans-serif" font-size="10" fill="#34495e">• 行为模式数据：工作习惯、效率波动、专注力变化、决策模式</text>
    </g>
    
    <g transform="translate(580, 80)">
      <text x="0" y="15" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">反馈优化机制：</text>
      <text x="0" y="35" font-family="Arial, sans-serif" font-size="10" fill="#34495e">• 实时反馈：任务执行过程中的即时调整和优化建议</text>
      <text x="0" y="48" font-family="Arial, sans-serif" font-size="10" fill="#34495e">• 周期性反馈：基于历史数据的模式分析和策略优化</text>
      <text x="0" y="61" font-family="Arial, sans-serif" font-size="10" fill="#34495e">• 自适应学习：通过强化学习持续改进系统决策能力</text>
    </g>
  </g>
  
  <!-- External System Connections -->
  <g transform="translate(50, 720)">
    <rect x="0" y="0" width="200" height="60" rx="8" fill="#95A5A6" stroke="#7F8C8D" stroke-width="2"/>
    <text x="100" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">外部系统接口</text>
    <text x="10" y="40" font-family="Arial, sans-serif" font-size="9" fill="white">• 第三方应用集成</text>
    <text x="10" y="52" font-family="Arial, sans-serif" font-size="9" fill="white">• API服务调用</text>
  </g>
  
  <g transform="translate(950, 720)">
    <rect x="0" y="0" width="200" height="60" rx="8" fill="#95A5A6" stroke="#7F8C8D" stroke-width="2"/>
    <text x="100" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">云服务平台</text>
    <text x="10" y="40" font-family="Arial, sans-serif" font-size="9" fill="white">• AI模型服务</text>
    <text x="10" y="52" font-family="Arial, sans-serif" font-size="9" fill="white">• 数据分析服务</text>
  </g>
  
  <!-- Connection lines to external systems -->
  <path d="M 150 720 L 200 520" stroke="#95A5A6" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
  <path d="M 1050 720 L 900 520" stroke="#95A5A6" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
  
  <!-- Legend -->
  <g transform="translate(400, 720)">
    <rect x="0" y="0" width="400" height="120" rx="5" fill="rgba(255,255,255,0.9)" stroke="#bdc3c7" stroke-width="1"/>
    <text x="200" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2c3e50">数据流图例</text>
    
    <line x1="20" y1="35" x2="45" y2="35" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
    <text x="55" y="40" font-family="Arial, sans-serif" font-size="10" fill="#34495e">同步数据流</text>
    
    <line x1="20" y1="50" x2="45" y2="50" stroke="#E67E22" stroke-width="2" stroke-dasharray="8,4" marker-end="url(#feedbackArrow)"/>
    <text x="55" y="55" font-family="Arial, sans-serif" font-size="10" fill="#34495e">反馈优化流</text>
    
    <line x1="20" y1="65" x2="45" y2="65" stroke="#F39C12" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="55" y="70" font-family="Arial, sans-serif" font-size="10" fill="#34495e">数据存储访问</text>
    
    <line x1="200" y1="35" x2="225" y2="35" stroke="#95A5A6" stroke-width="2" stroke-dasharray="3,3"/>
    <text x="235" y="40" font-family="Arial, sans-serif" font-size="10" fill="#34495e">外部系统集成</text>
    
    <text x="20" y="90" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">核心特性：</text>
    <text x="20" y="105" font-family="Arial, sans-serif" font-size="9" fill="#34495e">• 实时数据流处理 • 智能反馈循环 • 多引擎协同工作 • 持续学习优化</text>
  </g>
  </g>
</svg>