# BetterP 详细开发任务说明书

> **目的**: 为每个开发任务提供具体的实施步骤、技术选型、验收标准
> **使用方式**: 开发前必读，确保实施方向正确，避免返工
> **更新规则**: 任务开始前完善细节，完成后记录实际实施情况

---

## 🏗️ Phase 1.1: 架构设计与规划 (第1周)

### ARCH-001: 整体系统架构设计
**目标**: 确定系统整体架构模式，为后续开发提供指导

**具体实施步骤**:
1. **需求分析**:
   - 分析智能助手的功能需求（语音交互、系统控制、生活服务）
   - 评估用户规模（10万用户）和并发需求
   - 确定性能指标（响应时间<200ms，可用性99.9%）

2. **架构选型**:
   - 评估单体 vs 微服务架构
   - 推荐：**渐进式架构** - 从单体开始，预留微服务化空间
   - 核心服务：用户服务、任务服务、语音服务、系统控制服务

3. **技术栈确定**:
   - 后端：Python 3.11 + FastAPI + SQLAlchemy + Alembic
   - 数据库：PostgreSQL 14+ (主) + Redis 7+ (缓存)
   - 前端：Flutter 3.16+ + Riverpod + Dio
   - 网关：Kong 或 Nginx + Lua

**交付物**:
- [ ] 系统架构图 (draw.io 格式)
- [ ] 技术选型文档 (markdown)
- [ ] 服务拆分方案 (包含接口定义)
- [ ] 数据流图

**验收标准**:
- 架构图清晰展示各组件关系
- 技术选型有充分的对比分析
- 服务边界清晰，职责单一
- 支持水平扩展和微服务化

---

### ARCH-002: API 网关架构设计
**目标**: 设计统一的API网关，作为所有请求的入口

**具体实施步骤**:
1. **网关选型对比**:
   - Kong vs Nginx vs Zuul vs 自研
   - 评估维度：性能、功能、运维复杂度、社区支持
   - 推荐：**Kong** (功能丰富，插件生态好)

2. **功能设计**:
   - 路由规则：`/api/v1/users/*` -> 用户服务
   - 认证方式：JWT Token 验证
   - 限流策略：用户级别 100req/min，IP级别 1000req/min
   - 熔断机制：错误率>50% 时熔断 30s

3. **插件配置**:
   - rate-limiting: 限流控制
   - jwt: JWT 认证
   - cors: 跨域处理
   - prometheus: 监控指标
   - file-log: 访问日志

**交付物**:
- [ ] 网关架构设计文档
- [ ] Kong 配置文件模板
- [ ] 路由规则配置
- [ ] 插件配置说明

**验收标准**:
- 支持动态路由配置
- JWT 认证正常工作
- 限流功能有效
- 监控指标可采集

---

## 🔧 Phase 1.2: 开发环境配置 (第1周)

### ENV-001: Python 3.11 开发环境配置
**目标**: 搭建标准化的Python开发环境

**具体实施步骤**:
1. **Python 环境**:
   ```bash
   # 使用 pyenv 管理 Python 版本
   pyenv install 3.11.7
   pyenv local 3.11.7

   # 创建虚拟环境
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或 venv\Scripts\activate  # Windows
   ```

2. **依赖管理**:
   ```bash
   # 安装 poetry 进行依赖管理
   pip install poetry
   poetry init

   # 核心依赖
   poetry add fastapi uvicorn sqlalchemy alembic psycopg2-binary redis pydantic
   poetry add --group dev pytest black isort mypy pre-commit
   ```

3. **代码质量工具**:
   ```bash
   # 配置 pre-commit
   pre-commit install

   # .pre-commit-config.yaml 配置
   # - black (代码格式化)
   # - isort (import 排序)
   # - mypy (类型检查)
   # - flake8 (代码检查)
   ```

**交付物**:
- [ ] pyproject.toml 配置文件
- [ ] .pre-commit-config.yaml
- [ ] requirements.txt (导出用)
- [ ] 开发环境搭建文档

**验收标准**:
- Python 3.11.7 正常运行
- 所有依赖安装成功
- pre-commit 钩子正常工作
- 代码格式化和类型检查通过

---

### ENV-002: PostgreSQL 本地开发环境
**目标**: 配置本地PostgreSQL数据库环境

**具体实施步骤**:
1. **数据库安装**:
   ```bash
   # macOS
   brew install postgresql@14
   brew services start postgresql@14

   # Ubuntu
   sudo apt install postgresql-14 postgresql-contrib
   sudo systemctl start postgresql

   # Windows
   # 下载官方安装包安装
   ```

2. **数据库配置**:
   ```sql
   -- 创建开发数据库和用户
   CREATE USER betterp_dev WITH PASSWORD 'dev_password_123';
   CREATE DATABASE betterp_dev OWNER betterp_dev;
   GRANT ALL PRIVILEGES ON DATABASE betterp_dev TO betterp_dev;

   -- 创建测试数据库
   CREATE DATABASE betterp_test OWNER betterp_dev;
   ```

3. **连接配置**:
   ```python
   # config/database.py
   DATABASE_URL = "postgresql://betterp_dev:dev_password_123@localhost:5432/betterp_dev"
   TEST_DATABASE_URL = "postgresql://betterp_dev:dev_password_123@localhost:5432/betterp_test"
   ```

**交付物**:
- [ ] 数据库安装脚本
- [ ] 初始化SQL脚本
- [ ] 连接配置文件
- [ ] 数据库设计文档

**验收标准**:
- PostgreSQL 14+ 正常运行
- 开发和测试数据库创建成功
- Python 可正常连接数据库
- 支持 SSL 连接

---

## 🌐 Phase 1.3: API 网关开发 (第2周)

### GATEWAY-001: API 网关框架搭建
**目标**: 搭建Kong网关并配置基础功能

**具体实施步骤**:
1. **Kong 安装**:
   ```bash
   # 使用 Docker 安装 Kong
   docker run -d --name kong-database \
     -p 5432:5432 \
     -e "POSTGRES_USER=kong" \
     -e "POSTGRES_DB=kong" \
     -e "POSTGRES_PASSWORD=kong" \
     postgres:13

   # 初始化数据库
   docker run --rm \
     --link kong-database:kong-database \
     -e "KONG_DATABASE=postgres" \
     -e "KONG_PG_HOST=kong-database" \
     -e "KONG_PG_USER=kong" \
     -e "KONG_PG_PASSWORD=kong" \
     kong:latest kong migrations bootstrap

   # 启动 Kong
   docker run -d --name kong \
     --link kong-database:kong-database \
     -e "KONG_DATABASE=postgres" \
     -e "KONG_PG_HOST=kong-database" \
     -e "KONG_PG_USER=kong" \
     -e "KONG_PG_PASSWORD=kong" \
     -e "KONG_PROXY_ACCESS_LOG=/dev/stdout" \
     -e "KONG_ADMIN_ACCESS_LOG=/dev/stdout" \
     -e "KONG_PROXY_ERROR_LOG=/dev/stderr" \
     -e "KONG_ADMIN_ERROR_LOG=/dev/stderr" \
     -e "KONG_ADMIN_LISTEN=0.0.0.0:8001" \
     -p 8000:8000 \
     -p 8443:8443 \
     -p 8001:8001 \
     -p 8444:8444 \
     kong:latest
   ```

2. **基础服务注册**:
   ```bash
   # 注册用户服务
   curl -i -X POST http://localhost:8001/services/ \
     --data "name=user-service" \
     --data "url=http://localhost:8080"

   # 创建路由
   curl -i -X POST http://localhost:8001/services/user-service/routes \
     --data "hosts[]=api.betterp.local" \
     --data "paths[]=/api/v1/users"
   ```

**交付物**:
- [ ] Kong 安装脚本
- [ ] 服务注册脚本
- [ ] 路由配置文件
- [ ] Kong 管理界面访问说明

**验收标准**:
- Kong 正常启动并监听端口
- 管理API (8001) 可访问
- 代理端口 (8000) 正常工作
- 基础路由配置成功

---

### GATEWAY-002: 路由配置与服务发现
**目标**: 配置完整的路由规则和服务发现机制

**具体实施步骤**:
1. **路由规则设计**:
   ```yaml
   # routes.yaml
   services:
     - name: user-service
       url: http://localhost:8080
       routes:
         - name: user-api
           paths: ["/api/v1/users", "/api/v1/auth"]
           methods: ["GET", "POST", "PUT", "DELETE"]

     - name: task-service
       url: http://localhost:8081
       routes:
         - name: task-api
           paths: ["/api/v1/tasks", "/api/v1/projects"]
           methods: ["GET", "POST", "PUT", "DELETE"]

     - name: voice-service
       url: http://localhost:8082
       routes:
         - name: voice-api
           paths: ["/api/v1/voice", "/api/v1/speech"]
           methods: ["POST", "GET"]
   ```

2. **服务健康检查**:
   ```bash
   # 为每个服务配置健康检查
   curl -i -X POST http://localhost:8001/services/user-service/plugins \
     --data "name=http-log" \
     --data "config.http_endpoint=http://localhost:8083/logs"
   ```

**交付物**:
- [ ] 完整路由配置文件
- [ ] 服务注册自动化脚本
- [ ] 健康检查配置
- [ ] 路由测试用例

---

## 🔐 Phase 1.3: API 网关安全配置

### GATEWAY-003: 统一认证中间件
**目标**: 在网关层实现JWT认证，统一处理用户身份验证

**具体实施步骤**:
1. **JWT 插件配置**:
   ```bash
   # 安装 JWT 插件
   curl -X POST http://localhost:8001/services/user-service/plugins \
     --data "name=jwt" \
     --data "config.secret_is_base64=false" \
     --data "config.key_claim_name=iss" \
     --data "config.claims_to_verify=exp"
   ```

2. **JWT 密钥管理**:
   ```python
   # backend/app/core/security.py
   import jwt
   from datetime import datetime, timedelta

   SECRET_KEY = "your-secret-key-here"  # 生产环境使用环境变量
   ALGORITHM = "HS256"
   ACCESS_TOKEN_EXPIRE_MINUTES = 30

   def create_access_token(data: dict):
       to_encode = data.copy()
       expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
       to_encode.update({"exp": expire})
       encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
       return encoded_jwt

   def verify_token(token: str):
       try:
           payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
           return payload
       except jwt.PyJWTError:
           return None
   ```

3. **认证流程设计**:
   ```
   1. 用户登录 -> 后端验证 -> 返回JWT Token
   2. 前端存储Token -> 请求时携带Authorization Header
   3. Kong网关验证Token -> 通过则转发到后端服务
   4. 后端服务从Header获取用户信息
   ```

**交付物**:
- [ ] JWT 插件配置脚本
- [ ] Token 生成和验证代码
- [ ] 认证流程文档
- [ ] 测试用例和示例

**验收标准**:
- 无效Token请求被拒绝 (401)
- 有效Token正常通过
- Token过期自动失效
- 支持Token刷新机制

---

### GATEWAY-004: 限流与熔断机制
**目标**: 保护后端服务免受过载，提高系统稳定性

**具体实施步骤**:
1. **限流配置**:
   ```bash
   # 用户级限流 (每分钟100次)
   curl -X POST http://localhost:8001/services/user-service/plugins \
     --data "name=rate-limiting" \
     --data "config.minute=100" \
     --data "config.policy=local" \
     --data "config.fault_tolerant=true"

   # IP级限流 (每分钟1000次)
   curl -X POST http://localhost:8001/routes/user-api/plugins \
     --data "name=rate-limiting" \
     --data "config.minute=1000" \
     --data "config.policy=local" \
     --data "config.limit_by=ip"
   ```

2. **熔断器配置**:
   ```bash
   # 安装熔断器插件
   curl -X POST http://localhost:8001/services/user-service/plugins \
     --data "name=proxy-cache" \
     --data "config.response_code=200,301,404" \
     --data "config.request_method=GET,HEAD" \
     --data "config.content_type=text/plain,application/json" \
     --data "config.cache_ttl=300" \
     --data "config.strategy=memory"
   ```

3. **监控指标**:
   ```python
   # 关键监控指标
   - 请求QPS (每秒请求数)
   - 响应时间 (P50, P95, P99)
   - 错误率 (4xx, 5xx)
   - 限流触发次数
   - 熔断器状态
   ```

**交付物**:
- [ ] 限流策略配置
- [ ] 熔断器配置
- [ ] 监控指标定义
- [ ] 压力测试报告

**验收标准**:
- 限流正确触发并返回429
- 熔断器在高错误率时生效
- 监控指标正确采集
- 系统在压力下保持稳定

---

## 💻 Phase 1.4: 后端核心服务 (第2-3周)

### BACKEND-001: FastAPI 项目结构搭建
**目标**: 建立标准化的FastAPI项目结构

**具体实施步骤**:
1. **项目目录结构**:
   ```
   backend/
   ├── app/
   │   ├── __init__.py
   │   ├── main.py              # FastAPI 应用入口
   │   ├── core/                # 核心配置
   │   │   ├── __init__.py
   │   │   ├── config.py        # 配置管理
   │   │   ├── security.py      # 安全相关
   │   │   └── database.py      # 数据库连接
   │   ├── api/                 # API 路由
   │   │   ├── __init__.py
   │   │   ├── deps.py          # 依赖注入
   │   │   └── v1/              # API v1
   │   │       ├── __init__.py
   │   │       ├── auth.py      # 认证相关
   │   │       ├── users.py     # 用户管理
   │   │       └── tasks.py     # 任务管理
   │   ├── models/              # 数据模型
   │   │   ├── __init__.py
   │   │   ├── user.py
   │   │   └── task.py
   │   ├── schemas/             # Pydantic 模型
   │   │   ├── __init__.py
   │   │   ├── user.py
   │   │   └── task.py
   │   ├── services/            # 业务逻辑
   │   │   ├── __init__.py
   │   │   ├── user_service.py
   │   │   └── task_service.py
   │   └── utils/               # 工具函数
   │       ├── __init__.py
   │       └── helpers.py
   ├── tests/                   # 测试代码
   ├── alembic/                 # 数据库迁移
   ├── requirements.txt
   └── pyproject.toml
   ```

2. **FastAPI 应用配置**:
   ```python
   # app/main.py
   from fastapi import FastAPI
   from fastapi.middleware.cors import CORSMiddleware
   from app.core.config import settings
   from app.api.v1 import auth, users, tasks

   app = FastAPI(
       title="BetterP API",
       description="智能个人助手API",
       version="1.0.0",
       docs_url="/docs",
       redoc_url="/redoc"
   )

   # CORS 配置
   app.add_middleware(
       CORSMiddleware,
       allow_origins=settings.ALLOWED_HOSTS,
       allow_credentials=True,
       allow_methods=["*"],
       allow_headers=["*"],
   )

   # 注册路由
   app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
   app.include_router(users.router, prefix="/api/v1/users", tags=["用户"])
   app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["任务"])
   ```

**交付物**:
- [ ] 完整项目目录结构
- [ ] FastAPI 应用配置
- [ ] 基础中间件配置
- [ ] 项目结构说明文档

**验收标准**:
- 项目结构清晰合理
- FastAPI 应用正常启动
- API 文档可访问 (/docs)
- 支持热重载开发

---

## 📱 Phase 1.5: 前端基础架构 (第3-4周)

### FRONTEND-001: Flutter 项目初始化
**目标**: 创建标准化的Flutter项目结构

**具体实施步骤**:
1. **项目创建**:
   ```bash
   # 创建 Flutter 项目
   flutter create betterp_app
   cd betterp_app

   # 配置 Flutter 版本
   flutter --version  # 确保 3.16+
   ```

2. **项目结构设计**:
   ```
   lib/
   ├── main.dart                # 应用入口
   ├── app/                     # 应用配置
   │   ├── app.dart            # 主应用
   │   ├── routes.dart         # 路由配置
   │   └── theme.dart          # 主题配置
   ├── core/                   # 核心功能
   │   ├── constants/          # 常量定义
   │   ├── errors/             # 错误处理
   │   ├── network/            # 网络请求
   │   └── utils/              # 工具函数
   ├── features/               # 功能模块
   │   ├── auth/               # 认证模块
   │   │   ├── data/           # 数据层
   │   │   ├── domain/         # 业务层
   │   │   └── presentation/   # 表现层
   │   ├── tasks/              # 任务模块
   │   └── voice/              # 语音模块
   ├── shared/                 # 共享组件
   │   ├── widgets/            # 通用组件
   │   ├── models/             # 数据模型
   │   └── services/           # 共享服务
   └── generated/              # 生成代码
   ```

**交付物**:
- [ ] Flutter 项目初始化
- [ ] 项目结构搭建
- [ ] 基础配置文件
- [ ] 开发规范文档

**验收标准**:
- Flutter 项目正常运行
- 项目结构清晰合理
- 支持热重载
- 代码规范配置完成

这样的详细任务说明确保每个开发人员都知道具体要做什么、怎么做、做到什么程度才算完成。
**验收标准**:
- 所有服务路由正确
- 健康检查正常工作
- 支持动态路由更新
- 路由冲突检测有效
