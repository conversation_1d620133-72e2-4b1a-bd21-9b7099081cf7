# 支付系统架构设计

## 1. 会员体系

### 会员等级
- **免费版（Free）**: 基础功能
- **高级会员（Premium）**: ¥19.9/月 - AI功能增强
- **超级会员（Ultimate）**: ¥39.9/月 - 全功能解锁

### 会员权限对比

| 功能 | 免费版 | 高级会员 | 超级会员 |
|------|--------|----------|----------|
| 基础任务管理 | ✅ | ✅ | ✅ |
| 语音输入 | ✅ | ✅ | ✅ |
| AI任务分解 | ❌ | ✅ | ✅ |
| 智能提醒 | 每日5次 | 无限制 | 无限制 |
| 语音控制 | ❌ | ✅ | ✅ |
| 高级AI模型 | ❌ | ❌ | ✅ |
| 数据导出 | ❌ | ✅ | ✅ |
| 云同步 | 7天 | 无限制 | 无限制 |
| 优先客服 | ❌ | ❌ | ✅ |

## 2. 支付集成方案

### 支付渠道
1. **支付宝支付** - 主要渠道
2. **微信支付** - 主要渠道  
3. **Apple Pay** - iOS专用
4. **银行卡支付** - 备用方案

### 技术选型
- **后端**: FastAPI + SQLAlchemy
- **支付网关**: 
  - 支付宝开放平台 API
  - 微信支付 API V3
  - 苹果内购 StoreKit 2
- **数据库**: PostgreSQL

## 3. 数据库设计

### 支付相关表结构

#### 1. 会员订阅表 (user_subscriptions)
```sql
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    membership_tier VARCHAR(20) NOT NULL, -- 'free', 'premium', 'ultimate'
    status VARCHAR(20) NOT NULL, -- 'active', 'expired', 'cancelled', 'pending'
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    auto_renew BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. 支付订单表 (payment_orders)
```sql
CREATE TABLE payment_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    subscription_id UUID REFERENCES user_subscriptions(id),
    order_number VARCHAR(32) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CNY',
    payment_method VARCHAR(20) NOT NULL, -- 'alipay', 'wechat', 'apple_pay', 'bank_card'
    status VARCHAR(20) NOT NULL, -- 'pending', 'paid', 'failed', 'refunded', 'cancelled'
    third_party_order_id VARCHAR(64),
    paid_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. 支付日志表 (payment_logs)
```sql
CREATE TABLE payment_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES payment_orders(id),
    event_type VARCHAR(30) NOT NULL, -- 'create', 'callback', 'verify', 'refund'
    request_data TEXT,
    response_data TEXT,
    status_code INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 4. API接口设计

### 4.1 会员相关接口

#### 获取会员信息
```
GET /api/v1/membership/info
Headers: Authorization: Bearer <token>
Response: {
    "membership_tier": "premium",
    "status": "active",
    "expires_at": "2024-09-23T10:00:00Z",
    "auto_renew": true
}
```

#### 升级会员
```
POST /api/v1/membership/upgrade
Headers: Authorization: Bearer <token>
Body: {
    "tier": "premium", // "premium" or "ultimate"
    "payment_method": "alipay" // "alipay", "wechat", "apple_pay"
}
Response: {
    "order_id": "uuid",
    "payment_url": "https://...",
    "qr_code": "base64_image"
}
```

### 4.2 支付相关接口

#### 创建支付订单
```
POST /api/v1/payments/create
Headers: Authorization: Bearer <token>
Body: {
    "subscription_id": "uuid",
    "payment_method": "alipay",
    "return_url": "app://payment/success"
}
Response: {
    "order_id": "uuid",
    "order_number": "BP202408230001",
    "payment_url": "https://...",
    "amount": 19.9,
    "expires_at": "2024-08-23T11:00:00Z"
}
```

#### 支付回调处理
```
POST /api/v1/payments/callback/{payment_method}
Body: {
    // 第三方支付平台回调数据
}
Response: {
    "code": "SUCCESS"
}
```

#### 查询支付状态
```
GET /api/v1/payments/{order_id}/status
Headers: Authorization: Bearer <token>
Response: {
    "status": "paid",
    "paid_at": "2024-08-23T10:30:00Z",
    "payment_method": "alipay"
}
```

## 5. 安全设计

### 5.1 支付安全
- 所有支付金额使用分为单位存储
- 支付订单15分钟内有效
- 回调验签机制
- 重复支付检测
- 敏感信息加密存储

### 5.2 会员验证
- JWT Token包含会员等级信息
- 实时权限检查
- 过期自动降级处理
- 防刷机制

## 6. 实施计划

### Phase 1: 数据库和基础服务 (2天)
- [ ] 创建支付相关数据表
- [ ] 实现会员管理服务
- [ ] 实现订单管理服务

### Phase 2: 支付宝集成 (3天)
- [ ] 支付宝SDK集成
- [ ] 支付宝支付流程
- [ ] 支付宝回调处理
- [ ] 支付状态同步

### Phase 3: 微信支付集成 (3天)
- [ ] 微信支付SDK集成
- [ ] 微信支付流程
- [ ] 微信回调处理
- [ ] 统一支付接口

### Phase 4: 前端支付页面 (2天)
- [ ] 支付方式选择页面
- [ ] 支付结果页面
- [ ] 会员中心页面
- [ ] 订单历史页面

### Phase 5: Apple Pay集成 (3天)
- [ ] StoreKit 2集成
- [ ] 内购商品配置
- [ ] 收据验证
- [ ] 订阅管理

### Phase 6: 测试和优化 (2天)
- [ ] 支付流程测试
- [ ] 异常场景处理
- [ ] 性能优化
- [ ] 安全测试

## 7. 监控和运维

### 7.1 关键指标监控
- 支付成功率
- 支付响应时间
- 会员转化率
- 退款率
- 异常订单数

### 7.2 告警机制
- 支付失败率超过5%告警
- 回调延迟超过30秒告警
- 异常订单增长告警

### 7.3 日志管理
- 支付流程全链路日志
- 敏感信息脱敏
- 日志持久化存储
- 便于问题排查

## 8. 风险控制

### 8.1 业务风险
- 重复支付检测
- 恶意刷单防护
- 异常退款监控
- 账户安全验证

### 8.2 技术风险
- 支付接口降级方案
- 数据库备份策略
- 服务高可用设计
- 灾难恢复计划