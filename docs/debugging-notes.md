# BetterP 调试笔记

## 常见错误和解决方案

### 1. TypeError: string indices must be integers, not 'str'

**问题描述**:
- 在使用FastAPI依赖注入时，错误地将`get_current_user_token`的返回值当作字典处理
- 实际上`get_current_user_token`返回的是字符串（user_id），而不是包含用户信息的字典

**错误代码**:
```python
# ❌ 错误的用法
async def some_endpoint(
    user_token: Dict = Depends(get_current_user_token)
):
    user_id = user_token["user_id"]  # 这里会报错：string indices must be integers
```

**正确代码**:
```python
# ✅ 正确的用法
async def some_endpoint(
    user_id: str = Depends(get_current_user_token)
):
    # user_id 直接就是字符串，不需要从字典中提取
```

**根本原因**:
- `get_current_user_token`函数返回的是解析JWT token后得到的user_id字符串
- 不是包含多个字段的字典对象

**排查方法**:
1. 检查`app/api/dependencies.py`中的函数定义和返回值
2. 查看具体的异常堆栈，确定是哪个变量被错误地当作字典处理
3. 确认依赖注入函数的实际返回类型

**影响范围**:
- 所有使用`get_current_user_token`的API端点
- 特别是新创建的chat相关端点

**修复检查清单**:
- [ ] 检查所有`user_token: Dict = Depends(get_current_user_token)`的用法
- [ ] 替换为`user_id: str = Depends(get_current_user_token)`
- [ ] 移除所有`user_id = user_token["user_id"]`的代码
- [ ] 测试所有相关端点确保工作正常

---

### 2. 中间件超时问题排查步骤

**问题表现**:
- API请求超时（10秒、30秒或2分钟）
- 请求无法到达后端业务逻辑

**排查步骤**:
1. **逐步禁用中间件**:
   - 先禁用自定义中间件（rate limiting, logging, security）
   - 只保留FastAPI内置中间件测试基础功能
   
2. **检查日志中的ERROR信息**:
   ```bash
   docker-compose logs backend | grep ERROR
   ```

3. **从简单端点开始测试**:
   - 先测试不需要认证的端点（如/ping）
   - 再测试需要认证的端点
   - 最后测试复杂的业务逻辑

4. **检查依赖注入问题**:
   - 确认所有Depends()的返回类型正确
   - 特别注意用户认证相关的依赖

---

## 调试最佳实践

1. **逐步排除法**: 从最简单的配置开始，逐步添加复杂性
2. **日志先行**: 确保能看到详细的错误信息
3. **类型检查**: 特别注意依赖注入的返回类型
4. **快速反馈**: 使用简单的测试端点快速验证修复

---

*最后更新: 2025-08-22*