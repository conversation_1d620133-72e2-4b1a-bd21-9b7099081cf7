# Flutter语音输入组件规范

## 概述

本文档定义了BetterP应用中Flutter语音输入组件的设计规范、技术实现和集成接口，确保前端语音输入功能与后端语音识别服务的完美协作。

---

## 1. 技术架构

### 1.1 核心组件架构

```
Flutter 语音输入组件
├── SpeechInputWidget (主组件)
├── SpeechRecognitionService (服务层)
├── SpeechStateManager (状态管理)
├── SpeechPermissionHandler (权限处理)
├── SpeechAnimationController (动画控制)
└── SpeechConfigurationManager (配置管理)
```

### 1.2 平台支持

- **iOS**: Speech Framework + AVAudioEngine
- **Android**: SpeechRecognizer + AudioRecord
- **Web**: Web Speech API (可选)

---

## 2. 主要组件设计

### 2.1 SpeechInputWidget

**主要语音输入UI组件**

```dart
class SpeechInputWidget extends StatefulWidget {
  const SpeechInputWidget({
    Key? key,
    required this.onSpeechResult,
    this.onSpeechError,
    this.onSpeechStart,
    this.onSpeechEnd,
    this.language = 'zh-CN',
    this.enableRealTime = true,
    this.enableNoiseReduction = true,
    this.enableEchoCancellation = true,
    this.maxRecordingDuration = Duration(seconds: 60),
    this.confidenceThreshold = 0.6,
    this.customUI,
  }) : super(key: key);

  // 回调函数
  final Function(SpeechRecognitionResult) onSpeechResult;
  final Function(String)? onSpeechError;
  final VoidCallback? onSpeechStart;
  final VoidCallback? onSpeechEnd;
  
  // 配置参数
  final String language;
  final bool enableRealTime;
  final bool enableNoiseReduction;
  final bool enableEchoCancellation;
  final Duration maxRecordingDuration;
  final double confidenceThreshold;
  
  // 自定义UI
  final Widget? customUI;
}
```

### 2.2 SpeechRecognitionService

**语音识别服务封装**

```dart
class SpeechRecognitionService {
  static final SpeechRecognitionService _instance = SpeechRecognitionService._internal();
  factory SpeechRecognitionService() => _instance;
  SpeechRecognitionService._internal();

  // 核心方法
  Future<bool> initialize({
    String language = 'zh-CN',
    bool enableOnDevice = true,
  });
  
  Future<bool> startListening({
    required Function(SpeechRecognitionResult) onResult,
    Function(String)? onError,
    Duration? timeout,
  });
  
  Future<void> stopListening();
  Future<void> cancelListening();
  
  // 配置方法
  Future<void> setLanguage(String language);
  Future<void> updateConfig(SpeechConfig config);
  
  // 状态查询
  bool get isListening;
  bool get isAvailable;
  bool get hasPermission;
  
  // 能力查询
  Future<List<String>> getSupportedLanguages();
  Future<SpeechCapabilities> getCapabilities();
}
```

### 2.3 数据模型

#### SpeechRecognitionResult

```dart
class SpeechRecognitionResult {
  final String recognizedText;
  final double confidence;
  final String language;
  final bool isFinal;
  final List<String> alternatives;
  final Duration recordingDuration;
  final Map<String, dynamic> metadata;
  
  const SpeechRecognitionResult({
    required this.recognizedText,
    required this.confidence,
    this.language = 'zh-CN',
    this.isFinal = true,
    this.alternatives = const [],
    this.recordingDuration = Duration.zero,
    this.metadata = const {},
  });
}
```

#### SpeechConfig

```dart
class SpeechConfig {
  final String language;
  final bool enableRealTime;
  final bool enableNoiseReduction;
  final bool enableEchoCancellation;
  final Duration maxRecordingDuration;
  final double confidenceThreshold;
  final bool enableAlternatives;
  final bool enableProfanityFilter;
  final bool enableAutoPunctuation;
  
  const SpeechConfig({
    this.language = 'zh-CN',
    this.enableRealTime = true,
    this.enableNoiseReduction = true,
    this.enableEchoCancellation = true,
    this.maxRecordingDuration = const Duration(seconds: 60),
    this.confidenceThreshold = 0.6,
    this.enableAlternatives = true,
    this.enableProfanityFilter = false,
    this.enableAutoPunctuation = true,
  });
}
```

---

## 3. UI设计规范

### 3.1 默认UI样式

**录音按钮设计**

```dart
// 状态: 未开始录音
Container(
  width: 64,
  height: 64,
  decoration: BoxDecoration(
    color: Theme.of(context).primaryColor,
    shape: BoxShape.circle,
    boxShadow: [
      BoxShadow(
        color: Theme.of(context).primaryColor.withOpacity(0.3),
        blurRadius: 8,
        spreadRadius: 2,
      ),
    ],
  ),
  child: Icon(
    Icons.mic,
    color: Colors.white,
    size: 32,
  ),
)

// 状态: 正在录音
AnimatedContainer(
  duration: Duration(milliseconds: 300),
  width: 80,
  height: 80,
  decoration: BoxDecoration(
    color: Colors.red,
    shape: BoxShape.circle,
    boxShadow: [
      BoxShadow(
        color: Colors.red.withOpacity(0.4),
        blurRadius: 12,
        spreadRadius: 4,
      ),
    ],
  ),
  child: Icon(
    Icons.stop,
    color: Colors.white,
    size: 36,
  ),
)
```

**音量指示器**

```dart
class VoiceLevelIndicator extends StatelessWidget {
  final double level; // 0.0 - 1.0

  Widget build(BuildContext context) {
    return Container(
      height: 4,
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        color: Colors.grey[300],
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: level,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: _getLevelColor(level),
          ),
        ),
      ),
    );
  }
}
```

### 3.2 实时转录显示

```dart
class RealTimeTranscription extends StatefulWidget {
  final Stream<SpeechRecognitionResult> speechStream;
  final TextStyle? textStyle;
  final int maxLines;

  Widget build(BuildContext context) {
    return StreamBuilder<SpeechRecognitionResult>(
      stream: speechStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Container(
            height: 60,
            child: Center(
              child: Text(
                '点击开始语音输入...',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
          );
        }

        final result = snapshot.data!;
        return Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                result.recognizedText,
                style: textStyle ?? TextStyle(fontSize: 16),
                maxLines: maxLines,
                overflow: TextOverflow.ellipsis,
              ),
              if (!result.isFinal) ...[
                SizedBox(height: 4),
                Text(
                  '正在识别中...',
                  style: TextStyle(
                    color: Colors.blue,
                    fontSize: 12,
                  ),
                ),
              ],
              if (result.confidence > 0) ...[
                SizedBox(height: 4),
                Text(
                  '置信度: ${(result.confidence * 100).toInt()}%',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
```

---

## 4. 平台实现

### 4.1 iOS实现

**使用iOS Speech Framework**

```swift
// SpeechRecognitionPlugin.swift
import Speech
import AVFoundation

class SpeechRecognitionPlugin: NSObject, FlutterPlugin {
    private let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()
    
    static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "speech_recognition", 
                                         binaryMessenger: registrar.messenger())
        let instance = SpeechRecognitionPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "startListening":
            startListening(result: result)
        case "stopListening":
            stopListening(result: result)
        case "hasPermission":
            checkPermission(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func startListening(result: @escaping FlutterResult) {
        guard let speechRecognizer = speechRecognizer, 
              speechRecognizer.isAvailable else {
            result(FlutterError(code: "SPEECH_NOT_AVAILABLE", 
                              message: "语音识别不可用", details: nil))
            return
        }
        
        // 配置音频会话
        let audioSession = AVAudioSession.sharedInstance()
        try? audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
        try? audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        
        // 创建识别请求
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            result(FlutterError(code: "REQUEST_FAILED", 
                              message: "无法创建识别请求", details: nil))
            return
        }
        
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.requiresOnDeviceRecognition = true
        
        // 开始识别任务
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { 
            [weak self] (speechResult, error) in
            
            if let speechResult = speechResult {
                let resultData: [String: Any] = [
                    "recognizedText": speechResult.bestTranscription.formattedString,
                    "confidence": speechResult.bestTranscription.segments.first?.confidence ?? 0.0,
                    "isFinal": speechResult.isFinal,
                    "alternatives": speechResult.transcriptions.map { $0.formattedString }
                ]
                
                // 通过Method Channel发送结果
                self?.sendSpeechResult(resultData)
            }
            
            if let error = error {
                self?.handleSpeechError(error)
            }
        }
        
        // 配置音频输入
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { 
            (buffer, when) in
            recognitionRequest.append(buffer)
        }
        
        audioEngine.prepare()
        try? audioEngine.start()
        
        result(true)
    }
}
```

### 4.2 Android实现

**使用Android SpeechRecognizer**

```kotlin
// SpeechRecognitionPlugin.kt
class SpeechRecognitionPlugin : FlutterPlugin, MethodCallHandler {
    private var speechRecognizer: SpeechRecognizer? = null
    private var context: Context? = null
    private var channel: MethodChannel? = null
    
    override fun onAttachedToEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        context = binding.applicationContext
        channel = MethodChannel(binding.binaryMessenger, "speech_recognition")
        channel?.setMethodCallHandler(this)
    }
    
    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "startListening" -> startListening(call, result)
            "stopListening" -> stopListening(result)
            "hasPermission" -> checkPermission(result)
            else -> result.notImplemented()
        }
    }
    
    private fun startListening(call: MethodCall, result: Result) {
        val context = this.context ?: run {
            result.error("CONTEXT_NULL", "Context is null", null)
            return
        }
        
        if (!SpeechRecognizer.isRecognitionAvailable(context)) {
            result.error("SPEECH_NOT_AVAILABLE", "语音识别不可用", null)
            return
        }
        
        speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context).apply {
            setRecognitionListener(object : RecognitionListener {
                override fun onResults(results: Bundle?) {
                    val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    val confidences = results?.getFloatArray(SpeechRecognizer.CONFIDENCE_SCORES)
                    
                    if (!matches.isNullOrEmpty()) {
                        val resultData = mapOf(
                            "recognizedText" to matches[0],
                            "confidence" to (confidences?.get(0)?.toDouble() ?: 0.0),
                            "isFinal" to true,
                            "alternatives" to matches
                        )
                        
                        channel?.invokeMethod("onSpeechResult", resultData)
                    }
                }
                
                override fun onPartialResults(partialResults: Bundle?) {
                    val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    if (!matches.isNullOrEmpty()) {
                        val resultData = mapOf(
                            "recognizedText" to matches[0],
                            "confidence" to 0.0,
                            "isFinal" to false,
                            "alternatives" to matches
                        )
                        
                        channel?.invokeMethod("onSpeechResult", resultData)
                    }
                }
                
                override fun onError(error: Int) {
                    val errorMessage = when (error) {
                        SpeechRecognizer.ERROR_AUDIO -> "音频录制错误"
                        SpeechRecognizer.ERROR_CLIENT -> "客户端错误"
                        SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "权限不足"
                        SpeechRecognizer.ERROR_NETWORK -> "网络错误"
                        SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "网络超时"
                        SpeechRecognizer.ERROR_NO_MATCH -> "无匹配结果"
                        SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "识别器忙碌"
                        SpeechRecognizer.ERROR_SERVER -> "服务器错误"
                        SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "语音超时"
                        else -> "未知错误"
                    }
                    
                    channel?.invokeMethod("onSpeechError", errorMessage)
                }
                
                override fun onBeginningOfSpeech() {
                    channel?.invokeMethod("onSpeechStart", null)
                }
                
                override fun onEndOfSpeech() {
                    channel?.invokeMethod("onSpeechEnd", null)
                }
                
                // 其他回调方法的空实现...
                override fun onReadyForSpeech(params: Bundle?) {}
                override fun onRmsChanged(rmsdB: Float) {
                    channel?.invokeMethod("onRmsChanged", rmsdB.toDouble())
                }
                override fun onBufferReceived(buffer: ByteArray?) {}
                override fun onEvent(eventType: Int, params: Bundle?) {}
            })
        }
        
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, call.argument<String>("language") ?: "zh-CN")
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5)
        }
        
        speechRecognizer?.startListening(intent)
        result.success(true)
    }
}
```

---

## 5. 后端集成接口

### 5.1 API调用规范

**发送语音识别结果到后端**

```dart
class BackendSpeechService {
  static const String baseUrl = 'https://api.betterp.com';
  
  // 发送前端识别结果
  static Future<Map<String, dynamic>> sendSpeechResult({
    required String text,
    required double confidence,
    required String language,
    String platform = '',
    String engineUsed = '',
    List<String> alternatives = const [],
    int durationMs = 0,
    Map<String, dynamic> metadata = const {},
  }) async {
    
    final formData = FormData.fromMap({
      'text': text,
      'confidence': confidence,
      'language': language,
      'platform': platform,
      'engine_used': engineUsed,
      'alternatives': jsonEncode(alternatives),
      'duration_ms': durationMs,
      'metadata': jsonEncode(metadata),
    });
    
    try {
      final response = await dio.post(
        '$baseUrl/api/speech/recognize/frontend',
        data: formData,
      );
      
      return response.data;
    } catch (e) {
      throw SpeechServiceException('发送语音结果失败: $e');
    }
  }
  
  // 上传音频文件识别（备用方案）
  static Future<Map<String, dynamic>> uploadAudioFile({
    required File audioFile,
    String language = 'zh-CN',
    bool useBackendOnly = false,
  }) async {
    
    final formData = FormData.fromMap({
      'audio_file': await MultipartFile.fromFile(
        audioFile.path,
        filename: audioFile.path.split('/').last,
      ),
      'language': language,
      'use_backend_only': useBackendOnly,
    });
    
    try {
      final response = await dio.post(
        '$baseUrl/api/speech/recognize/audio',
        data: formData,
      );
      
      return response.data;
    } catch (e) {
      throw SpeechServiceException('上传音频失败: $e');
    }
  }
  
  // 混合模式（前端结果+音频文件）
  static Future<Map<String, dynamic>> hybridRecognition({
    required String text,
    required double confidence,
    required String platform,
    File? audioFile,
    String language = 'zh-CN',
  }) async {
    
    final formData = FormData.fromMap({
      'text': text,
      'confidence': confidence,
      'platform': platform,
      'language': language,
    });
    
    if (audioFile != null) {
      formData.files.add(MapEntry(
        'audio_file',
        await MultipartFile.fromFile(
          audioFile.path,
          filename: audioFile.path.split('/').last,
        ),
      ));
    }
    
    try {
      final response = await dio.post(
        '$baseUrl/api/speech/recognize/hybrid',
        data: formData,
      );
      
      return response.data;
    } catch (e) {
      throw SpeechServiceException('混合识别失败: $e');
    }
  }
}
```

### 5.2 响应数据处理

**处理后端返回的增强结果**

```dart
class EnhancedSpeechResult {
  final String originalText;
  final String correctedText;
  final double confidence;
  final String languageDetected;
  final bool intentDetected;
  final int processingTimeMs;
  final List<String> alternatives;
  final List<String> warnings;
  
  EnhancedSpeechResult.fromJson(Map<String, dynamic> json)
      : originalText = json['text'] ?? '',
        correctedText = json['corrected_text'] ?? json['text'] ?? '',
        confidence = (json['confidence'] ?? 0.0).toDouble(),
        languageDetected = json['language_detected'] ?? 'zh-CN',
        intentDetected = json['intent_detected'] ?? false,
        processingTimeMs = json['processing_time_ms'] ?? 0,
        alternatives = List<String>.from(json['alternatives'] ?? []),
        warnings = List<String>.from(json['warnings'] ?? []);
}

// 使用示例
void handleBackendResponse(Map<String, dynamic> response) {
  if (response['success'] == true) {
    final enhancedResult = EnhancedSpeechResult.fromJson(response['data']);
    
    // 使用修正后的文本
    final finalText = enhancedResult.correctedText.isNotEmpty 
        ? enhancedResult.correctedText 
        : enhancedResult.originalText;
    
    // 更新UI
    setState(() {
      recognizedText = finalText;
      isIntentDetected = enhancedResult.intentDetected;
    });
    
    // 如果检测到意图，触发相应操作
    if (enhancedResult.intentDetected) {
      handleDetectedIntent(finalText);
    }
  } else {
    showError(response['error'] ?? '语音处理失败');
  }
}
```

---

## 6. 状态管理

### 6.1 使用Provider进行状态管理

```dart
class SpeechState extends ChangeNotifier {
  bool _isListening = false;
  bool _isInitialized = false;
  String _recognizedText = '';
  double _confidence = 0.0;
  String _currentLanguage = 'zh-CN';
  double _soundLevel = 0.0;
  List<String> _alternatives = [];
  String? _errorMessage;
  
  // Getters
  bool get isListening => _isListening;
  bool get isInitialized => _isInitialized;
  String get recognizedText => _recognizedText;
  double get confidence => _confidence;
  String get currentLanguage => _currentLanguage;
  double get soundLevel => _soundLevel;
  List<String> get alternatives => _alternatives;
  String? get errorMessage => _errorMessage;
  
  // 初始化语音服务
  Future<void> initializeSpeech() async {
    try {
      final success = await SpeechRecognitionService().initialize(
        language: _currentLanguage,
      );
      
      _isInitialized = success;
      _errorMessage = success ? null : '语音服务初始化失败';
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }
  
  // 开始语音识别
  Future<void> startListening() async {
    if (!_isInitialized) {
      await initializeSpeech();
    }
    
    if (!_isInitialized) return;
    
    try {
      _isListening = true;
      _errorMessage = null;
      notifyListeners();
      
      final success = await SpeechRecognitionService().startListening(
        onResult: _handleSpeechResult,
        onError: _handleSpeechError,
      );
      
      if (!success) {
        _isListening = false;
        _errorMessage = '无法开始语音识别';
        notifyListeners();
      }
    } catch (e) {
      _isListening = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }
  
  // 停止语音识别
  Future<void> stopListening() async {
    if (!_isListening) return;
    
    try {
      await SpeechRecognitionService().stopListening();
      _isListening = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }
  
  void _handleSpeechResult(SpeechRecognitionResult result) {
    _recognizedText = result.recognizedText;
    _confidence = result.confidence;
    _alternatives = result.alternatives;
    
    if (result.isFinal) {
      _isListening = false;
      // 发送到后端处理
      _sendToBackend(result);
    }
    
    notifyListeners();
  }
  
  void _handleSpeechError(String error) {
    _isListening = false;
    _errorMessage = error;
    notifyListeners();
  }
  
  Future<void> _sendToBackend(SpeechRecognitionResult result) async {
    try {
      final response = await BackendSpeechService.sendSpeechResult(
        text: result.recognizedText,
        confidence: result.confidence,
        language: result.language,
        platform: Platform.isIOS ? 'ios' : 'android',
        alternatives: result.alternatives,
        durationMs: result.recordingDuration.inMilliseconds,
      );
      
      // 处理后端响应
      if (response['success'] == true) {
        final enhancedResult = EnhancedSpeechResult.fromJson(response['data']);
        
        // 更新为修正后的文本
        if (enhancedResult.correctedText.isNotEmpty && 
            enhancedResult.correctedText != _recognizedText) {
          _recognizedText = enhancedResult.correctedText;
          notifyListeners();
        }
      }
    } catch (e) {
      print('发送语音结果到后端失败: $e');
    }
  }
  
  // 切换语言
  Future<void> setLanguage(String language) async {
    _currentLanguage = language;
    if (_isInitialized) {
      await SpeechRecognitionService().setLanguage(language);
    }
    notifyListeners();
  }
  
  // 清空结果
  void clearResult() {
    _recognizedText = '';
    _confidence = 0.0;
    _alternatives = [];
    _errorMessage = null;
    notifyListeners();
  }
}
```

---

## 7. 权限管理

### 7.1 权限检查和请求

```dart
class SpeechPermissionHandler {
  static Future<bool> checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status.isGranted;
  }
  
  static Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }
  
  static Future<bool> checkSpeechRecognitionPermission() async {
    if (Platform.isIOS) {
      // iOS需要额外的语音识别权限
      return await SpeechRecognitionService().hasPermission;
    } else {
      // Android通常只需要麦克风权限
      return await checkMicrophonePermission();
    }
  }
  
  static Future<void> showPermissionDialog(BuildContext context) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('需要麦克风权限'),
        content: Text('语音输入功能需要访问您的麦克风，请在设置中授予权限。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: Text('去设置'),
          ),
        ],
      ),
    );
  }
}
```

---

## 8. 使用示例

### 8.1 基础使用

```dart
class SpeechInputExample extends StatefulWidget {
  @override
  _SpeechInputExampleState createState() => _SpeechInputExampleState();
}

class _SpeechInputExampleState extends State<SpeechInputExample> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('语音输入示例')),
      body: Consumer<SpeechState>(
        builder: (context, speechState, child) {
          return Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                // 实时转录显示
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    speechState.recognizedText.isEmpty 
                        ? '点击下方按钮开始语音输入...'
                        : speechState.recognizedText,
                    style: TextStyle(fontSize: 16),
                    minLines: 3,
                    maxLines: 10,
                  ),
                ),
                
                SizedBox(height: 16),
                
                // 置信度显示
                if (speechState.confidence > 0)
                  Text('置信度: ${(speechState.confidence * 100).toInt()}%'),
                
                SizedBox(height: 16),
                
                // 错误信息
                if (speechState.errorMessage != null)
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      speechState.errorMessage!,
                      style: TextStyle(color: Colors.red[800]),
                    ),
                  ),
                
                Spacer(),
                
                // 语音输入按钮
                SpeechInputWidget(
                  onSpeechResult: (result) {
                    // 结果已通过SpeechState处理
                  },
                  onSpeechError: (error) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('语音识别错误: $error')),
                    );
                  },
                  enableRealTime: true,
                  language: 'zh-CN',
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
```

### 8.2 集成到聊天界面

```dart
class ChatInputWithSpeech extends StatefulWidget {
  final Function(String) onSendMessage;
  
  const ChatInputWithSpeech({Key? key, required this.onSendMessage}) : super(key: key);
  
  @override
  _ChatInputWithSpeechState createState() => _ChatInputWithSpeechState();
}

class _ChatInputWithSpeechState extends State<ChatInputWithSpeech> {
  final TextEditingController _textController = TextEditingController();
  bool _showSpeechInput = false;
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 语音输入切换按钮
          IconButton(
            onPressed: () {
              setState(() {
                _showSpeechInput = !_showSpeechInput;
              });
            },
            icon: Icon(
              _showSpeechInput ? Icons.keyboard : Icons.mic,
              color: _showSpeechInput ? Colors.blue : Colors.grey,
            ),
          ),
          
          // 输入区域
          Expanded(
            child: _showSpeechInput 
                ? Consumer<SpeechState>(
                    builder: (context, speechState, child) {
                      // 自动填充识别结果到文本框
                      if (speechState.recognizedText.isNotEmpty && 
                          _textController.text != speechState.recognizedText) {
                        _textController.text = speechState.recognizedText;
                      }
                      
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 文本输入框（显示识别结果）
                          TextField(
                            controller: _textController,
                            decoration: InputDecoration(
                              hintText: speechState.isListening 
                                  ? '正在听取语音输入...' 
                                  : '点击麦克风开始语音输入',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8
                              ),
                            ),
                            maxLines: null,
                          ),
                          
                          SizedBox(height: 8),
                          
                          // 语音输入控件
                          SpeechInputWidget(
                            onSpeechResult: (result) {
                              if (result.isFinal) {
                                _textController.text = result.recognizedText;
                              }
                            },
                            customUI: Container(
                              width: 48,
                              height: 48,
                              child: speechState.isListening
                                  ? _buildListeningIndicator()
                                  : _buildMicButton(),
                            ),
                          ),
                        ],
                      );
                    },
                  )
                : TextField(
                    controller: _textController,
                    decoration: InputDecoration(
                      hintText: '输入消息...',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12, vertical: 8
                      ),
                    ),
                    maxLines: null,
                  ),
          ),
          
          // 发送按钮
          IconButton(
            onPressed: _textController.text.trim().isEmpty 
                ? null 
                : () {
                    widget.onSendMessage(_textController.text.trim());
                    _textController.clear();
                    context.read<SpeechState>().clearResult();
                  },
            icon: Icon(Icons.send),
          ),
        ],
      ),
    );
  }
  
  Widget _buildListeningIndicator() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.red,
        shape: BoxShape.circle,
      ),
      child: Icon(Icons.stop, color: Colors.white),
    );
  }
  
  Widget _buildMicButton() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        shape: BoxShape.circle,
      ),
      child: Icon(Icons.mic, color: Colors.white),
    );
  }
}
```

---

## 9. 测试规范

### 9.1 单元测试

```dart
// test/speech_recognition_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  group('SpeechRecognitionService', () {
    late SpeechRecognitionService service;
    
    setUp(() {
      service = SpeechRecognitionService();
    });
    
    test('应该能够正确初始化', () async {
      final result = await service.initialize();
      expect(result, isTrue);
      expect(service.isAvailable, isTrue);
    });
    
    test('应该能够检测权限状态', () async {
      final hasPermission = await service.hasPermission;
      expect(hasPermission, isA<bool>());
    });
    
    test('识别结果应该包含必要字段', () {
      const result = SpeechRecognitionResult(
        recognizedText: '测试文本',
        confidence: 0.95,
        isFinal: true,
      );
      
      expect(result.recognizedText, equals('测试文本'));
      expect(result.confidence, equals(0.95));
      expect(result.isFinal, isTrue);
    });
  });
  
  group('SpeechState', () {
    late SpeechState speechState;
    
    setUp(() {
      speechState = SpeechState();
    });
    
    test('初始状态应该正确', () {
      expect(speechState.isListening, isFalse);
      expect(speechState.isInitialized, isFalse);
      expect(speechState.recognizedText, isEmpty);
      expect(speechState.confidence, equals(0.0));
    });
    
    test('开始监听应该更新状态', () async {
      await speechState.startListening();
      expect(speechState.isListening, isTrue);
    });
  });
}
```

### 9.2 集成测试

```dart
// integration_test/speech_integration_test.dart
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('语音输入集成测试', () {
    testWidgets('完整的语音输入流程', (WidgetTester tester) async {
      // 启动应用
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();
      
      // 检查麦克风权限
      final micButton = find.byIcon(Icons.mic);
      expect(micButton, findsOneWidget);
      
      // 点击麦克风按钮
      await tester.tap(micButton);
      await tester.pumpAndSettle();
      
      // 模拟语音输入结果
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'speech_recognition',
        const StandardMethodCodec().encodeMethodCall(
          const MethodCall('onSpeechResult', {
            'recognizedText': '测试语音输入',
            'confidence': 0.95,
            'isFinal': true,
            'alternatives': ['测试语音输入', '测试语音识别'],
          }),
        ),
        (data) {},
      );
      
      await tester.pumpAndSettle();
      
      // 验证结果显示
      expect(find.text('测试语音输入'), findsOneWidget);
      
      // 验证置信度显示
      expect(find.textContaining('置信度: 95%'), findsOneWidget);
    });
    
    testWidgets('权限拒绝处理', (WidgetTester tester) async {
      // 模拟权限拒绝
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'speech_recognition',
        const StandardMethodCodec().encodeMethodCall(
          const MethodCall('onSpeechError', 'INSUFFICIENT_PERMISSIONS'),
        ),
        (data) {},
      );
      
      await tester.pumpAndSettle();
      
      // 应该显示权限请求对话框
      expect(find.text('需要麦克风权限'), findsOneWidget);
    });
  });
}
```

---

## 10. 性能优化

### 10.1 资源管理

```dart
class SpeechResourceManager {
  static Timer? _inactivityTimer;
  static bool _isResourcesActive = false;
  
  // 启动资源
  static Future<void> activateResources() async {
    if (_isResourcesActive) return;
    
    await SpeechRecognitionService().initialize();
    _isResourcesActive = true;
    
    _resetInactivityTimer();
  }
  
  // 释放资源
  static Future<void> deactivateResources() async {
    if (!_isResourcesActive) return;
    
    await SpeechRecognitionService().dispose();
    _isResourcesActive = false;
    
    _inactivityTimer?.cancel();
    _inactivityTimer = null;
  }
  
  // 重置不活跃计时器
  static void _resetInactivityTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer(Duration(minutes: 5), () {
      deactivateResources();
    });
  }
  
  // 报告活动
  static void reportActivity() {
    _resetInactivityTimer();
  }
}
```

### 10.2 缓存和预加载

```dart
class SpeechCache {
  static final Map<String, List<String>> _languageModels = {};
  static final Map<String, double> _confidenceHistory = {};
  
  // 预加载语言模型
  static Future<void> preloadLanguageModel(String language) async {
    if (_languageModels.containsKey(language)) return;
    
    try {
      final languages = await SpeechRecognitionService()
          .getSupportedLanguages();
      _languageModels[language] = languages;
    } catch (e) {
      print('预加载语言模型失败: $e');
    }
  }
  
  // 记录置信度历史
  static void recordConfidence(String text, double confidence) {
    _confidenceHistory[text] = confidence;
    
    // 保持最近100条记录
    if (_confidenceHistory.length > 100) {
      final oldestKey = _confidenceHistory.keys.first;
      _confidenceHistory.remove(oldestKey);
    }
  }
  
  // 获取平均置信度
  static double getAverageConfidence() {
    if (_confidenceHistory.isEmpty) return 0.0;
    
    final total = _confidenceHistory.values
        .fold(0.0, (sum, confidence) => sum + confidence);
    return total / _confidenceHistory.length;
  }
}
```

---

## 11. 错误处理和调试

### 11.1 错误分类和处理

```dart
class SpeechErrorHandler {
  static void handleSpeechError(String error, BuildContext context) {
    String userMessage;
    String debugInfo;
    
    switch (error) {
      case 'PERMISSION_DENIED':
        userMessage = '需要麦克风权限才能使用语音输入';
        debugInfo = '用户拒绝了麦克风权限';
        _showPermissionDialog(context);
        return;
        
      case 'SPEECH_NOT_AVAILABLE':
        userMessage = '您的设备不支持语音识别功能';
        debugInfo = '设备不支持SpeechRecognition';
        break;
        
      case 'NETWORK_ERROR':
        userMessage = '网络连接异常，请检查网络设置';
        debugInfo = '语音识别网络请求失败';
        break;
        
      case 'AUDIO_ERROR':
        userMessage = '音频录制失败，请检查麦克风设置';
        debugInfo = '音频设备访问失败';
        break;
        
      case 'TIMEOUT':
        userMessage = '语音识别超时，请重试';
        debugInfo = '语音识别处理超时';
        break;
        
      default:
        userMessage = '语音识别出现问题，请重试';
        debugInfo = '未知错误: $error';
    }
    
    // 显示用户友好的错误信息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(userMessage),
        action: SnackBarAction(
          label: '重试',
          onPressed: () => _retryLastOperation(context),
        ),
      ),
    );
    
    // 记录调试信息
    _logError(debugInfo, error);
  }
  
  static void _logError(String message, String originalError) {
    print('SpeechError: $message');
    print('OriginalError: $originalError');
    print('Timestamp: ${DateTime.now().toIso8601String()}');
    
    // 在调试模式下可以发送到分析服务
    if (kDebugMode) {
      _sendErrorToAnalytics(message, originalError);
    }
  }
  
  static void _sendErrorToAnalytics(String message, String error) {
    // 发送错误信息到分析服务
    // FirebaseCrashlytics.instance.recordError(error, null);
  }
  
  static void _retryLastOperation(BuildContext context) {
    // 重试上次的语音操作
    context.read<SpeechState>().startListening();
  }
  
  static void _showPermissionDialog(BuildContext context) {
    SpeechPermissionHandler.showPermissionDialog(context);
  }
}
```

### 11.2 调试工具

```dart
class SpeechDebugPanel extends StatefulWidget {
  @override
  _SpeechDebugPanelState createState() => _SpeechDebugPanelState();
}

class _SpeechDebugPanelState extends State<SpeechDebugPanel> {
  bool _showDebugInfo = kDebugMode;
  
  @override
  Widget build(BuildContext context) {
    if (!_showDebugInfo) return SizedBox.shrink();
    
    return Consumer<SpeechState>(
      builder: (context, speechState, child) {
        return Card(
          margin: EdgeInsets.all(16),
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('语音识别调试信息', 
                     style: TextStyle(fontWeight: FontWeight.bold)),
                Divider(),
                _buildDebugRow('初始化状态', speechState.isInitialized.toString()),
                _buildDebugRow('监听状态', speechState.isListening.toString()),
                _buildDebugRow('当前语言', speechState.currentLanguage),
                _buildDebugRow('识别文本', speechState.recognizedText),
                _buildDebugRow('置信度', '${(speechState.confidence * 100).toInt()}%'),
                _buildDebugRow('声音级别', '${(speechState.soundLevel * 100).toInt()}%'),
                _buildDebugRow('备选结果', speechState.alternatives.join(', ')),
                if (speechState.errorMessage != null)
                  _buildDebugRow('错误信息', speechState.errorMessage!, 
                               textColor: Colors.red),
                
                SizedBox(height: 8),
                Row(
                  children: [
                    ElevatedButton(
                      onPressed: () => speechState.initializeSpeech(),
                      child: Text('重新初始化'),
                    ),
                    SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () => speechState.clearResult(),
                      child: Text('清空结果'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildDebugRow(String label, String value, {Color? textColor}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: textColor ?? Colors.black87,
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
```

---

## 12. 配置文件

### 12.1 pubspec.yaml依赖

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # 状态管理
  provider: ^6.0.5
  
  # 网络请求
  dio: ^5.3.0
  
  # 权限管理
  permission_handler: ^10.4.3
  
  # 设备信息
  device_info_plus: ^9.1.0
  
  # 文件选择
  file_picker: ^5.3.2
  
  # 音频处理（如果需要）
  audioplayers: ^5.1.0
  
  # 平台通道
  flutter/services: any

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mockito: ^5.4.2
  build_runner: ^2.4.6
```

### 12.2 Android配置

```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 权限声明 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    
    <application
        android:label="BetterP"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            
            <!-- Intent过滤器 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        
        <!-- 不要杀死后台音频会话 -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
```

### 12.3 iOS配置

```xml
<!-- ios/Runner/Info.plist -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- 应用基本信息 -->
    <key>CFBundleDisplayName</key>
    <string>BetterP</string>
    <key>CFBundleIdentifier</key>
    <string>com.betterp.app</string>
    
    <!-- 权限描述 -->
    <key>NSMicrophoneUsageDescription</key>
    <string>BetterP需要使用麦克风进行语音输入，帮助您更高效地创建任务和进行语音交互。</string>
    
    <key>NSSpeechRecognitionUsageDescription</key>
    <string>BetterP需要使用语音识别功能来理解您的语音指令，为您提供更智能的任务管理体验。</string>
    
    <!-- 音频会话配置 -->
    <key>UIBackgroundModes</key>
    <array>
        <string>audio</string>
    </array>
    
    <!-- 支持的方向 -->
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
</dict>
</plist>
```

---

## 总结

本规范详细定义了BetterP应用Flutter语音输入组件的完整实现方案，包括：

1. **技术架构**: 清晰的组件层次和平台支持
2. **核心组件**: 主要UI组件和服务类设计
3. **平台实现**: iOS和Android的原生实现细节
4. **后端集成**: 与语音识别API的完整对接方案
5. **状态管理**: Provider模式的状态管理实现
6. **权限处理**: 完整的权限检查和请求流程
7. **错误处理**: 全面的错误分类和用户友好的处理
8. **性能优化**: 资源管理和缓存策略
9. **测试规范**: 单元测试和集成测试示例
10. **配置文件**: 完整的项目配置要求

通过遵循这个规范，开发团队可以实现一个功能完整、用户体验良好、与后端服务深度集成的语音输入功能。